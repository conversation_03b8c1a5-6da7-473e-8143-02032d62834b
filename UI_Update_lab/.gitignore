# ===================================
# Git 忽略文件配置
# 适用于 Vue.js + TypeScript + Vite 项目
# ===================================

# ===================================
# Node.js 依赖和包管理器
# ===================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
.pnpm-store/
.yarn/
.npm/

# ===================================
# 构建输出目录
# ===================================
dist/
build/
.output/
.nuxt/
.next/
out/

# ===================================
# 开发服务器和缓存
# ===================================
.vite/
.cache/
.temp/
.tmp/
.turbo/

# ===================================
# 环境变量和配置文件
# ===================================
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# ===================================
# 编辑器和IDE配置
# ===================================
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.settings/
*.sublime-project
*.sublime-workspace

# ===================================
# 操作系统生成的文件
# ===================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# ===================================
# 日志文件
# ===================================
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ===================================
# TypeScript 编译产物
# ===================================
*.tsbuildinfo
.tscache/

# ===================================
# Vue.js 和 Vite 特定文件
# ===================================
auto-imports.d.ts
components.d.ts
.eslintcache
.stylelintcache

# ===================================
# 测试覆盖率报告
# ===================================
coverage/
*.lcov
.nyc_output/
.coverage/

# ===================================
# 运行时数据
# ===================================
pids/
*.pid
*.seed
*.pid.lock

# ===================================
# 可选的npm缓存目录
# ===================================
.npm
.eslintcache

# ===================================
# 可选的REPL历史
# ===================================
.node_repl_history

# ===================================
# 输出的二进制文件
# ===================================
*.tar
*.zip
*.7z
*.dmg
*.iso

# ===================================
# 运行时数据
# ===================================
*.pid
*.seed
*.pid.lock

# ===================================
# 目录用于检测工具
# ===================================
.nyc_output

# ===================================
# Grunt 中间存储
# ===================================
.grunt

# ===================================
# Bower 依赖目录
# ===================================
bower_components

# ===================================
# node-waf 配置
# ===================================
.lock-wscript

# ===================================
# 编译的二进制插件
# ===================================
build/Release

# ===================================
# 依赖目录
# ===================================
jspm_packages/

# ===================================
# TypeScript v1 声明文件
# ===================================
typings/

# ===================================
# 可选的npm缓存目录
# ===================================
.npm

# ===================================
# 可选的eslint缓存
# ===================================
.eslintcache

# ===================================
# 微束缓存
# ===================================
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# ===================================
# 可选的REPL历史
# ===================================
.node_repl_history

# ===================================
# 输出的二进制文件
# ===================================
*.tgz

# ===================================
# Yarn完整性文件
# ===================================
.yarn-integrity

# ===================================
# dotenv环境变量文件
# ===================================
.env.test

# ===================================
# parcel-bundler缓存
# ===================================
.parcel-cache

# ===================================
# Next.js构建输出
# ===================================
.next

# ===================================
# Nuxt.js构建/生成输出
# ===================================
.nuxt
dist

# ===================================
# Gatsby文件
# ===================================
.cache/
public

# ===================================
# Storybook构建输出
# ===================================
.out
.storybook-out

# ===================================
# 临时文件夹
# ===================================
.tmp
.temp

# ===================================
# Rush Stack编译器
# ===================================
.rush/

# ===================================
# 项目特定忽略
# ===================================
# 安装日志
npminstall-debug.log

# 备份文件
*.bak
*.backup

# 压缩文件
*.rar
*.tar.gz

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 证书文件
*.pem
*.key
*.crt

# 配置文件备份
*.conf.bak
*.config.bak
