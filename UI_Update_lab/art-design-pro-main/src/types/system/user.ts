/**
 * 用户相关类型定义
 * 基于RuoYi数据结构，保持字段名称与RuoYi一致
 */

import type { RuoyiQueryParams } from '@/types/http'

/** 用户信息 */
export interface User {
  /** 用户ID */
  userId?: number
  /** 部门ID */
  deptId?: number
  /** 用户账号 */
  userName: string
  /** 用户昵称 */
  nickName: string
  /** 用户邮箱 */
  email?: string
  /** 手机号码 */
  phonenumber?: string
  /** 用户性别（0男 1女 2未知） */
  sex?: string
  /** 头像地址 */
  avatar?: string
  /** 密码 */
  password?: string
  /** 帐号状态（0正常 1停用） */
  status?: string
  /** 删除标志（0代表存在 2代表删除） */
  delFlag?: string
  /** 最后登录IP */
  loginIp?: string
  /** 最后登录时间 */
  loginDate?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 备注 */
  remark?: string
  /** 部门对象 */
  dept?: Dept
  /** 角色对象 */
  roles?: Role[]
  /** 角色组 */
  roleIds?: number[]
  /** 岗位组 */
  postIds?: number[]
}

/** 部门信息 */
export interface Dept {
  /** 部门id */
  deptId?: number
  /** 父部门id */
  parentId?: number
  /** 祖级列表 */
  ancestors?: string
  /** 部门名称 */
  deptName: string
  /** 显示顺序 */
  orderNum?: number
  /** 负责人 */
  leader?: string
  /** 联系电话 */
  phone?: string
  /** 邮箱 */
  email?: string
  /** 部门状态（0正常 1停用） */
  status?: string
  /** 删除标志（0代表存在 2代表删除） */
  delFlag?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 子部门 */
  children?: Dept[]
}

/** 角色信息 */
export interface Role {
  /** 角色ID */
  roleId?: number
  /** 角色名称 */
  roleName: string
  /** 角色权限字符串 */
  roleKey: string
  /** 显示顺序 */
  roleSort?: number
  /** 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限） */
  dataScope?: string
  /** 菜单树选择项是否关联显示 */
  menuCheckStrictly?: boolean
  /** 部门树选择项是否关联显示 */
  deptCheckStrictly?: boolean
  /** 角色状态（0正常 1停用） */
  status?: string
  /** 删除标志（0代表存在 2代表删除） */
  delFlag?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 备注 */
  remark?: string
  /** 用户是否存在此角色标识 默认不存在 */
  flag?: boolean
  /** 菜单组 */
  menuIds?: number[]
  /** 部门组（数据权限） */
  deptIds?: number[]
}

/** 岗位信息 */
export interface Post {
  /** 岗位ID */
  postId?: number
  /** 岗位编码 */
  postCode: string
  /** 岗位名称 */
  postName: string
  /** 显示顺序 */
  postSort?: number
  /** 状态（0正常 1停用） */
  status?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 备注 */
  remark?: string
  /** 用户是否存在此岗位标识 默认不存在 */
  flag?: boolean
}

/** 用户查询参数 */
export interface UserQueryParams extends RuoyiQueryParams {
  /** 用户账号 */
  userName?: string
  /** 手机号码 */
  phonenumber?: string
  /** 状态（0正常 1停用） */
  status?: string
  /** 部门ID */
  deptId?: number
}

/** 部门查询参数 */
export interface DeptQueryParams {
  /** 部门名称 */
  deptName?: string
  /** 状态（0正常 1停用） */
  status?: string
}

/** 角色查询参数 */
export interface RoleQueryParams extends RuoyiQueryParams {
  /** 角色名称 */
  roleName?: string
  /** 权限字符 */
  roleKey?: string
  /** 状态（0正常 1停用） */
  status?: string
}

/** 岗位查询参数 */
export interface PostQueryParams extends RuoyiQueryParams {
  /** 岗位编码 */
  postCode?: string
  /** 岗位名称 */
  postName?: string
  /** 状态（0正常 1停用） */
  status?: string
}
