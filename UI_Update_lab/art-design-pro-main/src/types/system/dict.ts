/**
 * 字典相关类型定义
 * 基于RuoYi数据结构，保持字段名称与RuoYi一致
 */

import type { RuoyiQueryParams } from '@/types/http'

/** 字典类型 */
export interface DictType {
  /** 字典主键 */
  dictId?: number
  /** 字典名称 */
  dictName: string
  /** 字典类型 */
  dictType: string
  /** 状态（0正常 1停用） */
  status?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 备注 */
  remark?: string
}

/** 字典数据 */
export interface DictData {
  /** 字典编码 */
  dictCode?: number
  /** 字典排序 */
  dictSort?: number
  /** 字典标签 */
  dictLabel: string
  /** 字典键值 */
  dictValue: string
  /** 字典类型 */
  dictType?: string
  /** 样式属性（其他样式扩展） */
  cssClass?: string
  /** 表格回显样式 */
  listClass?: string
  /** 是否默认（Y是 N否） */
  isDefault?: string
  /** 状态（0正常 1停用） */
  status?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 备注 */
  remark?: string
}

/** 字典类型查询参数 */
export interface DictTypeQueryParams extends RuoyiQueryParams {
  /** 字典名称 */
  dictName?: string
  /** 字典类型 */
  dictType?: string
  /** 状态（0正常 1停用） */
  status?: string
}

/** 字典数据查询参数 */
export interface DictDataQueryParams extends RuoyiQueryParams {
  /** 字典类型 */
  dictType?: string
  /** 字典标签 */
  dictLabel?: string
  /** 状态（0正常 1停用） */
  status?: string
}

/** 字典选项 */
export interface DictOption {
  /** 字典标签 */
  label: string
  /** 字典键值 */
  value: string
  /** 样式属性 */
  cssClass?: string
  /** 表格回显样式 */
  listClass?: string
  /** 是否默认 */
  isDefault?: string
}

/** 参数配置 */
export interface Config {
  /** 参数主键 */
  configId?: number
  /** 参数名称 */
  configName: string
  /** 参数键名 */
  configKey: string
  /** 参数键值 */
  configValue: string
  /** 系统内置（Y是 N否） */
  configType?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 备注 */
  remark?: string
}

/** 参数配置查询参数 */
export interface ConfigQueryParams extends RuoyiQueryParams {
  /** 参数名称 */
  configName?: string
  /** 参数键名 */
  configKey?: string
  /** 系统内置（Y是 N否） */
  configType?: string
}

/** 通知公告 */
export interface Notice {
  /** 公告ID */
  noticeId?: number
  /** 公告标题 */
  noticeTitle: string
  /** 公告类型（1通知 2公告） */
  noticeType: string
  /** 公告内容 */
  noticeContent?: string
  /** 公告状态（0正常 1关闭） */
  status?: string
  /** 创建者 */
  createBy?: string
  /** 创建时间 */
  createTime?: string
  /** 更新者 */
  updateBy?: string
  /** 更新时间 */
  updateTime?: string
  /** 备注 */
  remark?: string
}

/** 通知公告查询参数 */
export interface NoticeQueryParams extends RuoyiQueryParams {
  /** 公告标题 */
  noticeTitle?: string
  /** 公告类型（1通知 2公告） */
  noticeType?: string
  /** 创建者 */
  createBy?: string
}
