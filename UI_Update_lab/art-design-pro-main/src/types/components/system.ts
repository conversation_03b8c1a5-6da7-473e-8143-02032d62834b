/**
 * 系统组件Props类型定义
 * 基于RuoYi数据结构，保持字段名称与RuoYi一致
 */

import type { User, Role, Dept, Post, Menu, DictData, DictOption } from '@/types/system'

/** 用户表单组件Props */
export interface UserFormProps {
  /** 是否显示对话框 */
  visible: boolean
  /** 表单类型：add-新增，edit-编辑 */
  type: 'add' | 'edit'
  /** 用户数据 */
  userData?: User
  /** 部门选项 */
  deptOptions?: Dept[]
  /** 角色选项 */
  roleOptions?: Role[]
  /** 岗位选项 */
  postOptions?: Post[]
}

/** 用户表格组件Props */
export interface UserTableProps {
  /** 表格数据 */
  data: User[]
  /** 加载状态 */
  loading?: boolean
  /** 选中的行 */
  selection?: User[]
}

/** 角色表单组件Props */
export interface RoleFormProps {
  /** 是否显示对话框 */
  visible: boolean
  /** 表单类型：add-新增，edit-编辑 */
  type: 'add' | 'edit'
  /** 角色数据 */
  roleData?: Role
  /** 菜单树数据 */
  menuTree?: Menu[]
  /** 部门树数据 */
  deptTree?: Dept[]
}

/** 角色表格组件Props */
export interface RoleTableProps {
  /** 表格数据 */
  data: Role[]
  /** 加载状态 */
  loading?: boolean
  /** 选中的行 */
  selection?: Role[]
}

/** 菜单表单组件Props */
export interface MenuFormProps {
  /** 是否显示对话框 */
  visible: boolean
  /** 表单类型：add-新增，edit-编辑 */
  type: 'add' | 'edit'
  /** 菜单数据 */
  menuData?: Menu
  /** 菜单树选项 */
  menuOptions?: Menu[]
}

/** 菜单表格组件Props */
export interface MenuTableProps {
  /** 表格数据 */
  data: Menu[]
  /** 加载状态 */
  loading?: boolean
  /** 是否展开所有 */
  expandAll?: boolean
}

/** 部门表单组件Props */
export interface DeptFormProps {
  /** 是否显示对话框 */
  visible: boolean
  /** 表单类型：add-新增，edit-编辑 */
  type: 'add' | 'edit'
  /** 部门数据 */
  deptData?: Dept
  /** 部门树选项 */
  deptOptions?: Dept[]
}

/** 部门表格组件Props */
export interface DeptTableProps {
  /** 表格数据 */
  data: Dept[]
  /** 加载状态 */
  loading?: boolean
  /** 是否展开所有 */
  expandAll?: boolean
}

/** 岗位表单组件Props */
export interface PostFormProps {
  /** 是否显示对话框 */
  visible: boolean
  /** 表单类型：add-新增，edit-编辑 */
  type: 'add' | 'edit'
  /** 岗位数据 */
  postData?: Post
}

/** 岗位表格组件Props */
export interface PostTableProps {
  /** 表格数据 */
  data: Post[]
  /** 加载状态 */
  loading?: boolean
  /** 选中的行 */
  selection?: Post[]
}

/** 字典标签组件Props */
export interface DictTagProps {
  /** 字典选项 */
  options: DictOption[]
  /** 字典值 */
  value: string | number
  /** 默认值 */
  default?: string
}

/** 字典选择组件Props */
export interface DictSelectProps {
  /** 字典类型 */
  dictType: string
  /** 选中值 */
  modelValue?: string | number | (string | number)[]
  /** 是否多选 */
  multiple?: boolean
  /** 占位符 */
  placeholder?: string
  /** 是否禁用 */
  disabled?: boolean
  /** 是否可清空 */
  clearable?: boolean
}

/** 字典单选组件Props */
export interface DictRadioProps {
  /** 字典类型 */
  dictType: string
  /** 选中值 */
  modelValue?: string | number
  /** 是否禁用 */
  disabled?: boolean
}

/** 字典复选框组件Props */
export interface DictCheckboxProps {
  /** 字典类型 */
  dictType: string
  /** 选中值 */
  modelValue?: (string | number)[]
  /** 是否禁用 */
  disabled?: boolean
}

/** 分页组件Props */
export interface PaginationProps {
  /** 当前页码 */
  currentPage: number
  /** 每页显示条目个数 */
  pageSize: number
  /** 总条目数 */
  total: number
  /** 每页显示个数选择器的选项设置 */
  pageSizes?: number[]
  /** 组件布局，子组件名用逗号分隔 */
  layout?: string
  /** 是否为小型分页 */
  small?: boolean
  /** 是否禁用 */
  disabled?: boolean
}

/** 右侧工具栏组件Props */
export interface RightToolbarProps {
  /** 是否显示搜索 */
  showSearch?: boolean
  /** 搜索状态 */
  search?: boolean
  /** 选中数量 */
  selectedNum?: number
  /** 是否显示刷新按钮 */
  showRefresh?: boolean
  /** 是否显示密度按钮 */
  showDensity?: boolean
  /** 是否显示列设置按钮 */
  showColumn?: boolean
}

/** 文件上传组件Props */
export interface FileUploadProps {
  /** 上传地址 */
  action: string
  /** 已上传的文件列表 */
  fileList?: any[]
  /** 是否支持多选文件 */
  multiple?: boolean
  /** 最大允许上传个数 */
  limit?: number
  /** 允许上传的文件类型 */
  fileType?: string[]
  /** 文件大小限制(MB) */
  fileSize?: number
  /** 是否显示提示 */
  isShowTip?: boolean
}

/** 图片上传组件Props */
export interface ImageUploadProps {
  /** 上传地址 */
  action: string
  /** 已上传的图片列表 */
  modelValue?: string[]
  /** 是否支持多选 */
  multiple?: boolean
  /** 最大允许上传个数 */
  limit?: number
  /** 文件大小限制(MB) */
  fileSize?: number
  /** 是否显示提示 */
  isShowTip?: boolean
}

/** 图片预览组件Props */
export interface ImagePreviewProps {
  /** 图片地址 */
  src: string
  /** 图片宽度 */
  width?: string | number
  /** 图片高度 */
  height?: string | number
  /** 预览图片列表 */
  previewSrcList?: string[]
}
