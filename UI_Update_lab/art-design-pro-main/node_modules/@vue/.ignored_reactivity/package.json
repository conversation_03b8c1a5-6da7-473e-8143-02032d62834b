{"_from": "@vue/reactivity@^3.4.35", "_id": "@vue/reactivity@3.5.19", "_inBundle": false, "_integrity": "sha512-4bueZg2qs5MSsK2dQk3sssV0cfvxb/QZntTC8v7J448GLgmfPkQ+27aDjlt40+XFqOwUq5yRxK5uQh14Fc9eVA==", "_location": "/@vue/reactivity", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@vue/reactivity@^3.4.35", "name": "@vue/reactivity", "escapedName": "@vue%2freactivity", "scope": "@vue", "rawSpec": "^3.4.35", "saveSpec": null, "fetchSpec": "^3.4.35"}, "_requiredBy": ["/", "/@vue/runtime-core", "/@vue/runtime-dom"], "_resolved": "https://registry.npmmirror.com/@vue/reactivity/-/reactivity-3.5.19.tgz", "_shasum": "c06f172da26e2fc74060cc490d1c3aaffc024622", "_spec": "@vue/reactivity@^3.4.35", "_where": "/Users/<USER>/Public/mklab/UI_Update_lab/art-design-pro-main", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueReactivity", "formats": ["esm-bundler", "esm-browser", "cjs", "global"]}, "bundleDependencies": false, "dependencies": {"@vue/shared": "3.5.19"}, "deprecated": false, "description": "@vue/reactivity", "exports": {".": {"types": "./dist/reactivity.d.ts", "node": {"production": "./dist/reactivity.cjs.prod.js", "development": "./dist/reactivity.cjs.js", "default": "./index.js"}, "module": "./dist/reactivity.esm-bundler.js", "import": "./dist/reactivity.esm-bundler.js", "require": "./index.js"}, "./*": "./*"}, "files": ["index.js", "dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/reactivity#readme", "jsdelivr": "dist/reactivity.global.js", "keywords": ["vue"], "license": "MIT", "main": "index.js", "module": "dist/reactivity.esm-bundler.js", "name": "@vue/reactivity", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/reactivity"}, "sideEffects": false, "types": "dist/reactivity.d.ts", "unpkg": "dist/reactivity.global.js", "version": "3.5.19"}