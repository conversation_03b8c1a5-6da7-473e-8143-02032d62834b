{"_from": "@vue/compiler-sfc@3.5.19", "_id": "@vue/compiler-sfc@3.5.19", "_inBundle": false, "_integrity": "sha512-YWCm1CYaJ+2RvNmhCwI7t3I3nU+hOrWGWMsn+Z/kmm1jy5iinnVtlmkiZwbLlbV1SRizX7vHsc0/bG5dj0zRTg==", "_location": "/@vue/compiler-sfc", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@vue/compiler-sfc@3.5.19", "name": "@vue/compiler-sfc", "escapedName": "@vue%2fcompiler-sfc", "scope": "@vue", "rawSpec": "3.5.19", "saveSpec": null, "fetchSpec": "3.5.19"}, "_requiredBy": ["#DEV:/", "/@vue/babel-plugin-resolve-type", "/vue"], "_resolved": "https://registry.npmmirror.com/@vue/compiler-sfc/-/compiler-sfc-3.5.19.tgz", "_shasum": "7f9792ad7de5d4be9b6a32129c75e1f6cd4da015", "_spec": "@vue/compiler-sfc@3.5.19", "_where": "/Users/<USER>/Public/mklab/UI_Update_lab/art-design-pro-main/node_modules/vue", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vuejs/core/issues"}, "buildOptions": {"name": "VueCompilerSFC", "formats": ["cjs", "esm-browser"], "prod": false, "enableNonBrowserBranches": true}, "bundleDependencies": false, "dependencies": {"@babel/parser": "^7.28.3", "@vue/compiler-core": "3.5.19", "@vue/compiler-dom": "3.5.19", "@vue/compiler-ssr": "3.5.19", "@vue/shared": "3.5.19", "estree-walker": "^2.0.2", "magic-string": "^0.30.17", "postcss": "^8.5.6", "source-map-js": "^1.2.1"}, "deprecated": false, "description": "@vue/compiler-sfc", "devDependencies": {"@babel/types": "^7.28.2", "@vue/consolidate": "^1.0.0", "hash-sum": "^2.0.0", "lru-cache": "10.1.0", "merge-source-map": "^1.1.0", "minimatch": "~10.0.3", "postcss-modules": "^6.0.1", "postcss-selector-parser": "^7.1.0", "pug": "^3.0.3", "sass": "^1.90.0"}, "exports": {".": {"types": "./dist/compiler-sfc.d.ts", "node": "./dist/compiler-sfc.cjs.js", "module": "./dist/compiler-sfc.esm-browser.js", "import": "./dist/compiler-sfc.esm-browser.js", "require": "./dist/compiler-sfc.cjs.js"}, "./*": "./*"}, "files": ["dist"], "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-sfc#readme", "keywords": ["vue"], "license": "MIT", "main": "dist/compiler-sfc.cjs.js", "module": "dist/compiler-sfc.esm-browser.js", "name": "@vue/compiler-sfc", "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-sfc"}, "types": "dist/compiler-sfc.d.ts", "version": "3.5.19"}