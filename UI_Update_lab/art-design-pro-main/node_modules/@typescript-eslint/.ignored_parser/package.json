{"_from": "@typescript-eslint/parser@^8.3.0", "_id": "@typescript-eslint/parser@8.40.0", "_inBundle": false, "_integrity": "sha512-jCNyAuXx8dr5KJMkecGmZ8KI61KBUhkCob+SD+C+I5+Y1FWI2Y3QmY4/cxMCC5WAsZqoEtEETVhUiUMIGCf6Bw==", "_location": "/@typescript-eslint/parser", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@typescript-eslint/parser@^8.3.0", "name": "@typescript-eslint/parser", "escapedName": "@typescript-eslint%2fparser", "scope": "@typescript-eslint", "rawSpec": "^8.3.0", "saveSpec": null, "fetchSpec": "^8.3.0"}, "_requiredBy": ["#DEV:/", "/typescript-eslint"], "_resolved": "https://registry.npmmirror.com/@typescript-eslint/parser/-/parser-8.40.0.tgz", "_shasum": "1bc9f3701ced29540eb76ff2d95ce0d52ddc7e69", "_spec": "@typescript-eslint/parser@^8.3.0", "_where": "/Users/<USER>/Public/mklab/UI_Update_lab/art-design-pro-main", "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "bundleDependencies": false, "dependencies": {"@typescript-eslint/scope-manager": "8.40.0", "@typescript-eslint/types": "8.40.0", "@typescript-eslint/typescript-estree": "8.40.0", "@typescript-eslint/visitor-keys": "8.40.0", "debug": "^4.3.4"}, "deprecated": false, "description": "An ESLint custom parser which leverages TypeScript ESTree", "devDependencies": {"@vitest/coverage-v8": "^3.1.3", "eslint": "*", "glob": "*", "rimraf": "*", "typescript": "*", "vitest": "^3.1.3"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "./package.json": "./package.json"}, "files": ["dist", "!*.tsbuil<PERSON><PERSON>", "README.md", "LICENSE"], "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "homepage": "https://typescript-eslint.io/packages/parser", "keywords": ["ast", "ecmascript", "javascript", "typescript", "parser", "syntax", "eslint"], "license": "MIT", "name": "@typescript-eslint/parser", "nx": {"name": "parser", "includedScripts": ["clean"], "targets": {"lint": {"command": "eslint"}}}, "peerDependencies": {"eslint": "^8.57.0 || ^9.0.0", "typescript": ">=4.8.4 <6.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/parser"}, "scripts": {"build": "yarn run -BT nx build", "clean": "rimraf dist/ coverage/", "format": "yarn run -T format", "lint": "yarn run -BT nx lint", "test": "yarn run -BT nx test", "typecheck": "yarn run -BT nx typecheck"}, "type": "commonjs", "version": "8.40.0"}