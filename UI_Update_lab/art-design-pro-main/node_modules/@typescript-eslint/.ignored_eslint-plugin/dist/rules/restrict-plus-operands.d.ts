export type Options = [
    {
        allowAny?: boolean;
        allowBoolean?: boolean;
        allowNullish?: boolean;
        allowNumberAndString?: boolean;
        allowRegExp?: boolean;
        skipCompoundAssignments?: boolean;
    }
];
export type MessageIds = 'bigintAndNumber' | 'invalid' | 'mismatched';
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<MessageIds, Options, import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=restrict-plus-operands.d.ts.map