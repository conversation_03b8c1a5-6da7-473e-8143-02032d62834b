import { TSESTree } from '@typescript-eslint/utils';
import type { InferMessageIdsTypeFromRule, InferOptionsTypeFromRule } from '../util';
declare const baseRule: import("@typescript-eslint/utils/ts-eslint").RuleModule<"unusedExpression", [{
    allowShortCircuit?: boolean;
    allowTaggedTemplates?: boolean;
    allowTernary?: boolean;
}], unknown, {
    ExpressionStatement(node: TSESTree.ExpressionStatement): void;
}>;
export type MessageIds = InferMessageIdsTypeFromRule<typeof baseRule>;
export type Options = InferOptionsTypeFromRule<typeof baseRule>;
declare const _default: import("@typescript-eslint/utils/ts-eslint").RuleModule<"unusedExpression", [{
    allowShortCircuit?: boolean;
    allowTaggedTemplates?: boolean;
    allowTernary?: boolean;
}], import("../../rules").ESLintPluginDocs, import("@typescript-eslint/utils/ts-eslint").RuleListener>;
export default _default;
//# sourceMappingURL=no-unused-expressions.d.ts.map