{"_from": "@vitejs/plugin-vue@^5.2.1", "_id": "@vitejs/plugin-vue@5.2.4", "_inBundle": false, "_integrity": "sha512-7Yx/SXSOcQq5HiiV3orevHUFn+pmMB4cgbEkDYgnkUWb0WfeQ/wa2yFv6D5ICiCQOVpjA7vYDXrC7AGO8yjDHA==", "_location": "/@vitejs/plugin-vue", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@vitejs/plugin-vue@^5.2.1", "name": "@vitejs/plugin-vue", "escapedName": "@vitejs%2fplugin-vue", "scope": "@vitejs", "rawSpec": "^5.2.1", "saveSpec": null, "fetchSpec": "^5.2.1"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmmirror.com/@vitejs/plugin-vue/-/plugin-vue-5.2.4.tgz", "_shasum": "9e8a512eb174bfc2a333ba959bbf9de428d89ad8", "_spec": "@vitejs/plugin-vue@^5.2.1", "_where": "/Users/<USER>/Public/mklab/UI_Update_lab/art-design-pro-main", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/vitejs/vite-plugin-vue/issues"}, "bundleDependencies": false, "deprecated": false, "description": "> Note: as of `vue` 3.2.13+ and `@vitejs/plugin-vue` 1.9.0+, `@vue/compiler-sfc` is no longer required as a peer dependency.", "devDependencies": {"@jridgewell/gen-mapping": "^0.3.8", "@jridgewell/trace-mapping": "^0.3.25", "debug": "^4.4.0", "rollup": "^4.40.2", "slash": "^5.1.0", "source-map-js": "^1.2.1", "vite": "^6.3.5", "vue": "^3.5.13"}, "engines": {"node": "^18.0.0 || >=20.0.0"}, "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.cjs"}}, "files": ["dist"], "homepage": "https://github.com/vitejs/vite-plugin-vue/tree/main/packages/plugin-vue#readme", "license": "MIT", "main": "./dist/index.cjs", "module": "./dist/index.mjs", "name": "@vitejs/plugin-vue", "peerDependencies": {"vite": "^5.0.0 || ^6.0.0", "vue": "^3.2.25"}, "repository": {"type": "git", "url": "git+https://github.com/vitejs/vite-plugin-vue.git", "directory": "packages/plugin-vue"}, "scripts": {"build": "unbuild && pnpm run patch-cjs", "dev": "unbuild --stub", "patch-cjs": "tsx ../../scripts/patchCJS.ts"}, "type": "commonjs", "types": "./dist/index.d.ts", "version": "5.2.4"}