# wangEditor for vue-next component

[![GitHub license](https://img.shields.io/badge/license-MIT-blue.svg)](https://github.com/facebook/react/blob/main/LICENSE) [![npm](https://img.shields.io/npm/v/@wangeditor/editor-for-vue/next.svg)](https://www.npmjs.com/package/@wangeditor/editor-for-vue/v/next) [![build status](https://github.com/wangeditor-team/wangEditor-for-vue3/actions/workflows/npm-publish.yml/badge.svg?branch=main)](https://github.com/wangeditor-team/wangEditor-for-vue3/actions)

[English documentation](./README-en.md)

## 介绍

基于 [wangEditor](https://www.wangeditor.com/) 封装的开箱即用的 [Vue3 组件](https://www.wangeditor.com/v5/for-frame.html#vue3)

## 安装

```shell
yarn add @wangeditor/editor
yarn add @wangeditor/editor-for-vue@next
```

## 使用

参考[文档](https://www.wangeditor.com/v5/for-frame.html#vue3)
