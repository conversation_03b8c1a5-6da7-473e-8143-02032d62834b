{"version": 3, "file": "index.js", "sources": ["../src/utils/create-info.ts", "../plugin-vue:export-helper", "../src/components/Editor.vue", "../src/components/Toolbar.vue"], "sourcesContent": ["/**\n * @description 错误提示信息\n * <AUTHOR>\n */\nexport function genErrorInfo(fnName: string): string {\n  let info = `请使用 '@${fnName}' 事件，不要放在 props 中`\n  info += `\\nPlease use '@${fnName}' event instead of props`\n  return info\n}", "\nexport default (sfc, props) => {\n  for (const [key, val] of props) {\n    sfc[key] = val\n  }\n  return sfc\n}\n", "<template>\n  <div ref=\"box\" style=\"height: 100%\"></div>\n</template>\n\n<script lang=\"ts\">\nimport { onMounted, defineComponent, ref, PropType, toRaw, watch, shallowRef } from 'vue'\nimport { createEditor, IEditorConfig, SlateDescendant, IDomEditor } from '@wangeditor/editor'\nimport { genErrorInfo } from '../utils/create-info'\n\nexport default defineComponent({\n  props: {\n    /** 编辑器模式 */\n    mode: {\n      type: String,\n      default: 'default',\n    },\n    /** 编辑器默认内容 */\n    defaultContent: {\n      type: Array as PropType<SlateDescendant[]>,\n      default: [],\n    },\n    defaultHtml: {\n      type: String,\n      default: '',\n    },\n    /** 编辑器默认配置 */\n    defaultConfig: {\n      type: Object as PropType<Partial<IEditorConfig>>,\n      default: {},\n    },\n    /* 自定义 v-model */\n    modelValue: {\n      type: String,\n      default: '',\n    },\n  },\n  setup(props, context) {\n    const box = ref(null) // 编辑器容器\n\n    const editorRef = shallowRef<null | IDomEditor>(null) // editor 实例，必须用 shallowRef\n\n    const curValue = ref('') // 记录 editor 当前 html 内容\n\n    /**\n     * 初始化编辑器\n     */\n    const initEditor = () => {\n      if (!box.value) return\n      // 获取原始数据，解除响应式特性\n      const defaultContent = toRaw(props.defaultContent)\n\n      createEditor({\n        selector: box.value! as Element,\n        mode: props.mode,\n        content: defaultContent || [],\n        html: props.defaultHtml || props.modelValue || '',\n        config: {\n          ...props.defaultConfig,\n          onCreated(editor) {\n            editorRef.value = editor // 记录 editor 实例\n\n            context.emit('onCreated', editor)\n\n            if (props.defaultConfig.onCreated) {\n              const info = genErrorInfo('onCreated')\n              throw new Error(info)\n            }\n          },\n          onChange(editor) {\n            const editorHtml = editor.getHtml()\n            curValue.value = editorHtml // 记录当前内容\n            context.emit('update:modelValue', editorHtml) // 触发 v-model 值变化\n\n            context.emit('onChange', editor)\n            if (props.defaultConfig.onChange) {\n              const info = genErrorInfo('onChange')\n              throw new Error(info)\n            }\n          },\n          onDestroyed(editor) {\n            context.emit('onDestroyed', editor)\n            if (props.defaultConfig.onDestroyed) {\n              const info = genErrorInfo('onDestroyed')\n              throw new Error(info)\n            }\n          },\n          onMaxLength(editor) {\n            context.emit('onMaxLength', editor)\n            if (props.defaultConfig.onMaxLength) {\n              const info = genErrorInfo('onMaxLength')\n              throw new Error(info)\n            }\n          },\n          onFocus(editor) {\n            context.emit('onFocus', editor)\n            if (props.defaultConfig.onFocus) {\n              const info = genErrorInfo('onFocus')\n              throw new Error(info)\n            }\n          },\n          onBlur(editor) {\n            context.emit('onBlur', editor)\n            if (props.defaultConfig.onBlur) {\n              const info = genErrorInfo('onBlur')\n              throw new Error(info)\n            }\n          },\n          customAlert(info, type) {\n            context.emit('customAlert', info, type)\n            // @ts-ignore\n            if (props.defaultConfig.customAlert) {\n              const info = genErrorInfo('customAlert')\n              throw new Error(info)\n            }\n          },\n          customPaste: (editor, event): any => {\n            if (props.defaultConfig.customPaste) {\n              const info = genErrorInfo('customPaste')\n              throw new Error(info)\n            }\n            let res\n            context.emit('customPaste', editor, event, (val: boolean) => {\n              res = val\n            })\n            return res\n          },\n        },\n      })\n    }\n\n    /**\n     * 设置 HTML\n     * @param newHtml new html\n     */\n    function setHtml(newHtml: string) {\n      const editor = editorRef.value\n      if (editor == null) return\n      editor.setHtml(newHtml)\n    }\n\n    /**\n     * 元素挂在后初始化编辑器\n     */\n    onMounted(() => {\n      initEditor()\n    })\n\n    // 监听 v-model 值变化\n    watch(\n      () => props.modelValue,\n      newVal => {\n        if (newVal === curValue.value) return // 和当前内容一样，则忽略\n\n        // 重新设置 HTML\n        setHtml(newVal)\n      }\n    )\n\n    return {\n      box,\n    }\n  },\n})\n</script>\n", "<template>\n  <div ref=\"selector\"></div>\n</template>\n<script lang=\"ts\">\nimport { defineComponent, ref, watchEffect, PropType } from 'vue'\nimport { createToolbar, IToolbarConfig, IDomEditor, DomEditor } from '@wangeditor/editor'\n\nexport default defineComponent({\n  props: {\n    // editor 实例\n    editor: {\n      type: Object as PropType<IDomEditor>\n    },\n    /** 编辑器模式 */\n    mode: {\n      type: String,\n      default: 'default',\n    },\n    /** 编辑器默认配置 */\n    defaultConfig: {\n      type: Object as PropType<Partial<IToolbarConfig>>,\n      default: {},\n    },\n  },\n  setup(props) {\n    // toolbar 容器\n    const selector = ref(null)\n\n    /**\n     * 初始化 toolbar\n     */\n    const create = (editor: IDomEditor) => {\n      if (!selector.value) return\n      if (editor == null) {\n        throw new Error('Not found instance of Editor when create <Toolbar/> component')\n      }\n      if (DomEditor.getToolbar(editor)) return // 不重复创建\n\n      createToolbar({\n        editor,\n        selector: (selector.value! as Element) || '<div></div>',\n        mode: props.mode,\n        config: props.defaultConfig,\n      })\n    }\n\n    watchEffect( () => {\n      const { editor } = props\n      if (editor == null) return\n      create(editor) // 初始化 toolbar\n    })\n\n    return {\n      selector,\n    }\n  },\n})\n</script>\n"], "names": ["_sfc_main", "defineComponent", "ref", "shallowRef", "toRaw", "_hoisted_1", "editor", "Dom<PERSON><PERSON>or"], "mappings": "6wBAI6B,EAAwB,IAC/C,GAAO,wBAAS,uEACZ;AAAA,eAAkB,4BACnB,QCNM,CAAC,EAAK,IAAU,CAC7B,SAAW,CAAC,EAAK,IAAQ,GACvB,EAAI,GAAO,EAEb,MAAO,ICIT,KAAKA,GAAaC,kBAAa,CAC7B,MAAO,CAEL,KAAM,CACJ,KAAM,OACN,QAAS,WAGX,eAAgB,CACd,KAAM,MACN,QAAS,IAEX,YAAa,CACX,KAAM,OACN,QAAS,IAGX,cAAe,CACb,KAAM,OACN,QAAS,IAGX,WAAY,CACV,KAAM,OACN,QAAS,KAGb,MAAM,EAAO,EAAS,MACd,GAAMC,MAAI,MAEV,EAAYC,aAA8B,MAE1C,EAAWD,MAAI,IAKf,EAAa,IAAM,IACnB,CAAC,EAAI,kBAEH,GAAiBE,QAAM,EAAM,+BAEtB,CACX,SAAU,EAAI,MACd,KAAM,EAAM,KACZ,QAAS,GAAkB,GAC3B,KAAM,EAAM,aAAe,EAAM,YAAc,GAC/C,OAAQ,OACH,EAAM,eADH,CAEN,UAAU,EAAQ,MACN,MAAQ,IAEV,KAAK,YAAa,GAEtB,EAAM,cAAc,UAAW,MAC3B,GAAO,EAAa,kBACpB,IAAI,OAAM,KAGpB,SAAS,EAAQ,MACT,GAAa,EAAO,eACjB,MAAQ,IACT,KAAK,oBAAqB,KAE1B,KAAK,WAAY,GACrB,EAAM,cAAc,SAAU,MAC1B,GAAO,EAAa,iBACpB,IAAI,OAAM,KAGpB,YAAY,EAAQ,MACV,KAAK,cAAe,GACxB,EAAM,cAAc,YAAa,MAC7B,GAAO,EAAa,oBACpB,IAAI,OAAM,KAGpB,YAAY,EAAQ,MACV,KAAK,cAAe,GACxB,EAAM,cAAc,YAAa,MAC7B,GAAO,EAAa,oBACpB,IAAI,OAAM,KAGpB,QAAQ,EAAQ,MACN,KAAK,UAAW,GACpB,EAAM,cAAc,QAAS,MACzB,GAAO,EAAa,gBACpB,IAAI,OAAM,KAGpB,OAAO,EAAQ,MACL,KAAK,SAAU,GACnB,EAAM,cAAc,OAAQ,MACxB,GAAO,EAAa,eACpB,IAAI,OAAM,KAGpB,YAAY,EAAM,EAAM,MACd,KAAK,cAAe,EAAM,GAE9B,EAAM,cAAc,YAAa,MAC7B,GAAO,EAAa,oBACpB,IAAI,OAAM,KAGpB,YAAa,CAAC,EAAQ,IAAe,IAC/B,EAAM,cAAc,YAAa,MAC7B,GAAO,EAAa,oBACpB,IAAI,OAAM,MAEd,YACI,KAAK,cAAe,EAAQ,EAAO,AAAC,GAAiB,GACrD,IAED,mBAUE,EAAiB,MAC1B,GAAS,EAAU,MACrB,GAAU,QACP,QAAQ,sBAMP,IAAM,eAMd,IAAM,EAAM,WACZ,GAAU,CACJ,IAAW,EAAS,SAGhB,KAIL,CACL,UA9JIC,EAAM,CAAC,IAAoB,0CAAnC,UAA0C,uFCM5C,KAAK,GAAaJ,kBAAa,CAC7B,MAAO,CAEL,OAAQ,CACN,KAAM,QAGR,KAAM,CACJ,KAAM,OACN,QAAS,WAGX,cAAe,CACb,KAAM,OACN,QAAS,KAGb,MAAM,EAAO,MAEL,GAAWC,MAAI,MAKf,EAAS,AAACI,GAAuB,IACjC,EAAC,EAAS,UACVA,GAAU,UACN,IAAI,OAAM,iEAEdC,YAAU,WAAWD,oBAEX,QACZA,EACA,SAAW,EAAS,OAAsB,cAC1C,KAAM,EAAM,KACZ,OAAQ,EAAM,uCAIL,IAAM,MACX,CAAE,UAAW,EACf,GAAU,QACP,KAGF,CACL,+CApDJ,UAA0B"}