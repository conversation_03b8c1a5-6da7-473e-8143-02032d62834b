{"_from": "@wangeditor/editor-for-vue@next", "_id": "@wangeditor/editor-for-vue@5.1.12", "_inBundle": false, "_integrity": "sha512-0Ds3D8I+xnpNWezAeO7HmPRgTfUxHLMd9JKcIw+QzvSmhC5xUHbpCcLU+KLmeBKTR/zffnS5GQo6qi3GhTMJWQ==", "_location": "/@wangeditor/editor-for-vue", "_phantomChildren": {}, "_requested": {"type": "tag", "registry": true, "raw": "@wangeditor/editor-for-vue@next", "name": "@wangeditor/editor-for-vue", "escapedName": "@wangeditor%2feditor-for-vue", "scope": "@wangeditor", "rawSpec": "next", "saveSpec": null, "fetchSpec": "next"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/@wangeditor/editor-for-vue/-/editor-for-vue-5.1.12.tgz", "_shasum": "f7d5f239b39cdfc01d31151488de8443fe6edc64", "_spec": "@wangeditor/editor-for-vue@next", "_where": "/Users/<USER>/Public/mklab/UI_Update_lab/art-design-pro-main", "author": {"name": "liuqh0609", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/wangeditor-team/wangEditor-for-vue3/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "wangEditor component for vue@next", "devDependencies": {"@babel/preset-env": "^7.15.8", "@testing-library/jest-dom": "^5.14.1", "@types/jest": "^27.0.2", "@types/lodash": "^4.14.172", "@types/node": "^16.7.1", "@typescript-eslint/parser": "^4.29.3", "@vitejs/plugin-vue": "^1.3.0", "@vue/compiler-sfc": "^3.0.5", "@vue/test-utils": "^2.0.0-rc.16", "@wangeditor/editor": "^5.1.1", "babel-jest": "^26.6.3", "jest": "25.5.4", "lodash": "^4.17.21", "prettier": "^2.5.1", "release-it": "^14.11.5", "ts-jest": "^25.3.1", "typescript": "^4.3.2", "vite": "^2.4.4", "vite-plugin-dts": "^0.7.0", "vue": "^3.2.7", "vue-jest": "^5.0.0-alpha.10", "vue-tsc": "^0.2.2"}, "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.js"}}, "files": ["dist"], "homepage": "http://www.wangeditor.com/", "keywords": ["wang<PERSON><PERSON><PERSON>", "富文本编辑器", "富文本", "vue3"], "license": "MIT", "main": "./dist/index.js", "module": "./dist/index.esm.js", "name": "@wangeditor/editor-for-vue", "peerDependencies": {"@wangeditor/editor": ">=5.1.0", "vue": "^3.0.5"}, "repository": {"type": "git", "url": "git+https://github.com/wangeditor-team/wangEditor-for-vue3.git"}, "scripts": {"build": "vue-tsc --noEmit && vite build", "dev": "vite", "release": "release-it", "serve": "vite preview", "test": "jest"}, "types": "dist/src/index.d.ts", "version": "5.1.12"}