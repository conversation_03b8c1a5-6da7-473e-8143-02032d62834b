{"_from": "@wangeditor/editor@^5.1.23", "_id": "@wangeditor/editor@5.1.23", "_inBundle": false, "_integrity": "sha512-0RxfeVTuK1tktUaPROnCoFfaHVJpRAIE2zdS0mpP+vq1axVQpLjM8+fCvKzqYIkH0Pg+C+44hJpe3VVroSkEuQ==", "_location": "/@wangeditor/editor", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@wangeditor/editor@^5.1.23", "name": "@wangeditor/editor", "escapedName": "@wangeditor%2feditor", "scope": "@wangeditor", "rawSpec": "^5.1.23", "saveSpec": null, "fetchSpec": "^5.1.23"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/@wangeditor/editor/-/editor-5.1.23.tgz", "_shasum": "c9d2007b7cb0ceef6b72692b4ee87b01ee2367b3", "_spec": "@wangeditor/editor@^5.1.23", "_where": "/Users/<USER>/Public/mklab/UI_Update_lab/art-design-pro-main", "author": {"name": "wangfupeng1988", "email": "<EMAIL>"}, "browser": {"./dist/index.js": "./dist/index.js", "./dist/index.esm.js": "./dist/index.esm.js"}, "bugs": {"url": "https://github.com/wangeditor-team/wangEditor/issues"}, "bundleDependencies": false, "contributors": [], "dependencies": {"@uppy/core": "^2.1.1", "@uppy/xhr-upload": "^2.0.3", "@wangeditor/basic-modules": "^1.1.7", "@wangeditor/code-highlight": "^1.0.3", "@wangeditor/core": "^1.1.19", "@wangeditor/list-module": "^1.0.5", "@wangeditor/table-module": "^1.1.4", "@wangeditor/upload-image-module": "^1.0.2", "@wangeditor/video-module": "^1.1.4", "dom7": "^3.0.0", "is-hotkey": "^0.2.0", "lodash.camelcase": "^4.3.0", "lodash.clonedeep": "^4.5.0", "lodash.debounce": "^4.0.8", "lodash.foreach": "^4.5.0", "lodash.isequal": "^4.5.0", "lodash.throttle": "^4.1.1", "lodash.toarray": "^4.4.0", "nanoid": "^3.2.0", "slate": "^0.72.0", "snabbdom": "^3.1.0"}, "deprecated": false, "description": "Web rich text editor, Web 富文本编辑器", "directories": {"lib": "dist", "test": "__tests__"}, "files": ["dist"], "gitHead": "75812c37496111f1b6e8121967db9c7f2c2ba46d", "homepage": "https://www.wangeditor.com/", "keywords": ["wangeditor", "rich text", "editor", "富文本", "编辑器"], "license": "MIT", "main": "dist/index.js", "module": "dist/index.esm.js", "name": "@wangeditor/editor", "publishConfig": {"access": "public", "registry": "https://registry.npmjs.com/"}, "repository": {"type": "git", "url": "git+https://github.com/wangeditor-team/wangEditor.git"}, "scripts": {"build": "cross-env NODE_ENV=production rollup -c rollup.config.js", "dev": "cross-env NODE_ENV=development rollup -c rollup.config.js", "dev-size-stats": "cross-env NODE_ENV=development:size_stats rollup -c rollup.config.js", "dev-watch": "cross-env NODE_ENV=development rollup -c rollup.config.js -w", "example": "concurrently \"yarn dev-watch\" \"http-server -p 8881 -c-1\" ", "size-stats": "cross-env NODE_ENV=production:size_stats rollup -c rollup.config.js", "test": "jest", "test-c": "jest --coverage"}, "types": "dist/editor/src/index.d.ts", "version": "5.1.23"}