# Change Log

All notable changes to this project will be documented in this file.
See [Conventional Commits](https://conventionalcommits.org) for commit guidelines.

## [5.1.23](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.22...@wangeditor/editor@5.1.23) (2022-11-14)

**Note:** Version bump only for package @wangeditor/editor





## [5.1.22](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.21...@wangeditor/editor@5.1.22) (2022-10-18)

**Note:** Version bump only for package @wangeditor/editor





## [5.1.21](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.20...@wangeditor/editor@5.1.21) (2022-10-04)

**Note:** Version bump only for package @wangeditor/editor





## [5.1.20](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.19...@wangeditor/editor@5.1.20) (2022-09-27)

**Note:** Version bump only for package @wangeditor/editor





## [5.1.19](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.18...@wangeditor/editor@5.1.19) (2022-09-27)

**Note:** Version bump only for package @wangeditor/editor





## [5.1.18](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.17...@wangeditor/editor@5.1.18) (2022-09-16)

**Note:** Version bump only for package @wangeditor/editor





## [5.1.17](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.16...@wangeditor/editor@5.1.17) (2022-09-15)


### Bug Fixes

* customInsert 不触发 onSuccess ([d6f4a1b](https://github.com/wangeditor-team/wangEditor/commit/d6f4a1b1494864b116a1310cce2d9e8632c92c6f))
* focus table 时 isFocused 异常 ([5c52bf3](https://github.com/wangeditor-team/wangEditor/commit/5c52bf33e91b1a4677e7bbc04c5d80698abfeeab))
* 上传视频 - customBrowseAndUpload 缺少 poster ([c24627a](https://github.com/wangeditor-team/wangEditor/commit/c24627aaa4c173c5d435e3077dfe8f6b4a9a87b1))





## [5.1.16](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.15...@wangeditor/editor@5.1.16) (2022-09-14)

**Note:** Version bump only for package @wangeditor/editor





## [5.1.15](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.14...@wangeditor/editor@5.1.15) (2022-08-30)


### Bug Fixes

* checkVideo 增加 poster 参数 ([c0402e1](https://github.com/wangeditor-team/wangEditor/commit/c0402e155470233d256e037d863dab74c026b7f6))





## [5.1.14](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.13...@wangeditor/editor@5.1.14) (2022-07-27)

**Note:** Version bump only for package @wangeditor/editor





## [5.1.13](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.12...@wangeditor/editor@5.1.13) (2022-07-27)


### Bug Fixes

* setHtml 支持空字符串 ([d438157](https://github.com/wangeditor-team/wangEditor/commit/d43815766320d9cb0548bae0415c54ce7b147efb))
* upload file callback error ([bf20e07](https://github.com/wangeditor-team/wangEditor/commit/bf20e07f12ed242b0ab4bb2290d876153a822972))





## [5.1.12](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.11...@wangeditor/editor@5.1.12) (2022-07-22)

**Note:** Version bump only for package @wangeditor/editor





## [5.1.11](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.10...@wangeditor/editor@5.1.11) (2022-07-18)

**Note:** Version bump only for package @wangeditor/editor





## [5.1.10](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.9...@wangeditor/editor@5.1.10) (2022-07-16)

**Note:** Version bump only for package @wangeditor/editor





## [5.1.9](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.8...@wangeditor/editor@5.1.9) (2022-07-14)

**Note:** Version bump only for package @wangeditor/editor





## [5.1.8](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.7...@wangeditor/editor@5.1.8) (2022-07-14)

**Note:** Version bump only for package @wangeditor/editor





## [5.1.7](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.6...@wangeditor/editor@5.1.7) (2022-07-13)

**Note:** Version bump only for package @wangeditor/editor





## [5.1.6](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.5...@wangeditor/editor@5.1.6) (2022-07-12)

**Note:** Version bump only for package @wangeditor/editor





## [5.1.5](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.3...@wangeditor/editor@5.1.5) (2022-07-11)


### Bug Fixes

* 尝试修复 nuxt 报错 issue[#4409](https://github.com/wangeditor-team/wangEditor/issues/4409) ([912f888](https://github.com/wangeditor-team/wangEditor/commit/912f8889a11d962b3ac2c65cb5835f4e8c58c372))





## [5.1.4](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.3...@wangeditor/editor@5.1.4) (2022-07-11)


### Bug Fixes

* 尝试修复 nuxt 报错 issue[#4409](https://github.com/wangeditor-team/wangEditor/issues/4409) ([912f888](https://github.com/wangeditor-team/wangEditor/commit/912f8889a11d962b3ac2c65cb5835f4e8c58c372))





## [5.1.3](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.2...@wangeditor/editor@5.1.3) (2022-07-11)


### Bug Fixes

* scroll 滚动问题 ([bc133e1](https://github.com/wangeditor-team/wangEditor/commit/bc133e1e4ca89ab5042cbc0971578ad144499805))





## [5.1.2](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.1...@wangeditor/editor@5.1.2) (2022-07-11)

**Note:** Version bump only for package @wangeditor/editor





## [5.1.1](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.1.0...@wangeditor/editor@5.1.1) (2022-06-02)

**Note:** Version bump only for package @wangeditor/editor





# [5.1.0](https://github.com/wangeditor-team/wangEditor/compare/@wangeditor/editor@5.0.1...@wangeditor/editor@5.1.0) (2022-05-25)


### Features

* editVideoSize ([375eecb](https://github.com/wangeditor-team/wangEditor/commit/****************************************))
* enter menu ([988fc31](https://github.com/wangeditor-team/wangEditor/commit/988fc31f31de3d37dffbf54abb784cceb8e6118d))
* setHtml ([f4f91b8](https://github.com/wangeditor-team/wangEditor/commit/f4f91b883298091e3679ca6b206ae0d796003772))





## 5.0.1 (2022-04-18)


### Bug Fixes

* 不支持 IE 浏览器的提醒 ([70c5cae](https://github.com/wangeditor-team/wangEditor/commit/70c5caefd8f6f663225b7a0b796a035d274ef4e1))
* 打包问题 ([c4e87cc](https://github.com/wangeditor-team/wangEditor/commit/c4e87ccac82bcf90d20b7304aff83745e52fb1b1))
* 更新各包之间依赖版本 ([75c552c](https://github.com/wangeditor-team/wangEditor/commit/75c552cc8ed54765bebb86a7ec5329a7fc79e85f))
* 兼容 AggregateError ([0cbd82d](https://github.com/wangeditor-team/wangEditor/commit/0cbd82d30d350b2313f6373e2b5f6d168e47e1bc))
* 兼容next.js及nuxt.js ([233728e](https://github.com/wangeditor-team/wangEditor/commit/233728eb984f541437c62a1390fa0542b2cc6227))
* 开放几个第三方用的 API ([bdf3e70](https://github.com/wangeditor-team/wangEditor/commit/bdf3e70c52bac71e2056e21237fe4ac9e2b0818f))
* 拼音隐藏 placeholder ([aec1a9f](https://github.com/wangeditor-team/wangEditor/commit/aec1a9f62af8944b7894beeca953076ec73545d5))
* 上传图片 - base64 仍触发上传 + 超出 maxSize 的报错提醒 ([a1d469a](https://github.com/wangeditor-team/wangEditor/commit/a1d469accb7f87f8ea0282a1699d002aaaa4e79a))
* 添加QQ浏览器polyfill ([a1b476a](https://github.com/wangeditor-team/wangEditor/commit/a1b476a0bed52315f3e398c586d73f85996f9431))
* 图片上传，提示 ([3754012](https://github.com/wangeditor-team/wangEditor/commit/37540129dff1212c5ebfd4ca3f4d4e8def735e73))
* 修复 node 环境下报错问题 ([5a635a5](https://github.com/wangeditor-team/wangEditor/commit/5a635a5e8fac942ee214dd22b097e09057abc69c))
* 修复取消链接后撤销再重做报错的问题 ([9b233a9](https://github.com/wangeditor-team/wangEditor/commit/9b233a92c95571235248623a6ca5212eb4237f2a))
* 移除了每个包下的 publishConfig directory 配置 ([16559f0](https://github.com/wangeditor-team/wangEditor/commit/16559f052545c111318be760e64291a521bdcc65))
* 优化选中代码块不应该展示 hoverbar 的交互 ([33dcbd6](https://github.com/wangeditor-team/wangEditor/commit/33dcbd6560dccfbe77e18cfbce8c9f077f19f6cd))
* delete divider ([f04cbd6](https://github.com/wangeditor-team/wangEditor/commit/f04cbd6009099629e3cd41be19d20b6788fe7f28))
* divider - 键盘删除 ([31db059](https://github.com/wangeditor-team/wangEditor/commit/31db0593dbc77fba9b4a719bc0f48f1223afd680))
* example/code-hightlight ([7885988](https://github.com/wangeditor-team/wangEditor/commit/78859884cefc18d15ce2f87507380a78c2ad65e5))
* globalThis 兼容性 ([7a47f4b](https://github.com/wangeditor-team/wangEditor/commit/7a47f4b904815516d3b5749ab652ff80478411bc))
* group-menu 考虑 excludeKeys ([ecc29f3](https://github.com/wangeditor-team/wangEditor/commit/ecc29f3b24992c8dc0adf006d81b0d4a252683c5))
* hoverbar config - 同时选中文字和 table ([8f6b4d1](https://github.com/wangeditor-team/wangEditor/commit/8f6b4d1a20e3b1b75da69b20bd5893ce08a27185))
* hoverbarKeys - text ([59b4840](https://github.com/wangeditor-team/wangEditor/commit/59b48406b4c373ef029a5f5bdb0d15d925a91a0f))
* html 特殊字符 ([b3eb81b](https://github.com/wangeditor-team/wangEditor/commit/b3eb81bc9c4aa15c2ff7451c173de15d6c4552bc))
* i18n - 获取多语言配置 ([9f81597](https://github.com/wangeditor-team/wangEditor/commit/9f815970f8c3c6dddb6bf846ecb672325e80444b))
* i18n 切换语言 ([b3b4642](https://github.com/wangeditor-team/wangEditor/commit/b3b4642c6e72ab0b13b05657745abb87e71c633d))
* insertKeys ([0a89420](https://github.com/wangeditor-team/wangEditor/commit/0a8942050bd0b39afb5bbc55ca7842461a5b98eb))
* link, text hoverbar 选区问题 ([e0b7438](https://github.com/wangeditor-team/wangEditor/commit/e0b7438c89a347f1b0b940d9c11150b72d595529))
* menu 点击多次才能生效 ([6497e39](https://github.com/wangeditor-team/wangEditor/commit/6497e39225a993c4d87f9ffddf20086446a4fbc2))
* normalize when create editor ([2b51962](https://github.com/wangeditor-team/wangEditor/commit/2b5196244a93ad7beb316bfa42e557221967d063))
* parse html - v4 video ([8dca822](https://github.com/wangeditor-team/wangEditor/commit/8dca822f9f1b52fd71dd6e17f0954d6aa016324b))
* qq 浏览器报错 ([8a09ed5](https://github.com/wangeditor-team/wangEditor/commit/8a09ed5d810fc1e2c4d0c529aa1269ed0c06425e))
* readOnly 时菜单还可操作 ([0d4a29b](https://github.com/wangeditor-team/wangEditor/commit/0d4a29bb5ba8b62ac11a09d3f814abcb1fcf46be))
* registerModule ([189981c](https://github.com/wangeditor-team/wangEditor/commit/189981c73db07d5b15ee4c46b1639f76f6f63ba1))
* rename es module filename ([1821d4e](https://github.com/wangeditor-team/wangEditor/commit/1821d4eef49e64efcb41b848849ca7a5e6472044))
* shadow dom 样式缺失 ([2fcb69c](https://github.com/wangeditor-team/wangEditor/commit/2fcb69c866266cc5b0265cff031ae9279d368b84))
* style-to-html - 输入 a 会删除外部的 <a> 标签 ([af1f523](https://github.com/wangeditor-team/wangEditor/commit/af1f523983f2bc4b7eaf9726d4b8a35227ab27dc))
* table - elemToHtml ([e36e609](https://github.com/wangeditor-team/wangEditor/commit/e36e6092ef721723169afc8bf0560a47ac9f4dfc))
* tableCell 中 br 报错 ([8604db7](https://github.com/wangeditor-team/wangEditor/commit/8604db751b622c01fa5391af59328236cf13effc))
* text hoverbar ([c7de4f8](https://github.com/wangeditor-team/wangEditor/commit/c7de4f815d6f5b9e009a3149ed042052576c424e))
* text hoverbar ([efe9a34](https://github.com/wangeditor-team/wangEditor/commit/efe9a34d85f8baaeced27543a7bcd508b50f6bca))
* video - 键盘删除 ([5a6bedd](https://github.com/wangeditor-team/wangEditor/commit/5a6bedd80fa0d758270731f62115637ad7f313d0))


### Features

* 两端对齐 ([e5080d3](https://github.com/wangeditor-team/wangEditor/commit/e5080d3dd102f7a951d8e1f370db834778ecbdfa))
* 上标 下标 ([40dab08](https://github.com/wangeditor-team/wangEditor/commit/40dab085a061ea3e838f0cfa86260c6c6f894c69))
* 上传图片 metaWithUrl ([2485157](https://github.com/wangeditor-team/wangEditor/commit/24851576a1dcc07b1a8931d17a147c3640222e85))
* 增加 enable disable API（删除 setConfig setMenuConfig API） ([984fc50](https://github.com/wangeditor-team/wangEditor/commit/984fc50520061fc34ea08f4136bdeb93dee46564))
* 支持 nodejs 环境 ([484f18c](https://github.com/wangeditor-team/wangEditor/commit/484f18c3abc70d19e51c556f48491c18d390b1e1))
* basic text style module ([005b343](https://github.com/wangeditor-team/wangEditor/commit/005b343573ba98f2d0b8480d034ff6807a499aa3))
* block quote ([c3c87a5](https://github.com/wangeditor-team/wangEditor/commit/c3c87a5c09b311eb14c799df94fc4826aa3f4262))
* bold & header ([8130c23](https://github.com/wangeditor-team/wangEditor/commit/8130c23ad84485a68cf9ca4b53d52fab1cec4e96))
* clearStyle menu ([8002f70](https://github.com/wangeditor-team/wangEditor/commit/8002f707ed04b914180ec36fdca0edf48c815e01))
* code highlight ([42b2f8d](https://github.com/wangeditor-team/wangEditor/commit/42b2f8d192e2433593c11ad0b8424737f6cffb58))
* code-block - part ([a8bcd63](https://github.com/wangeditor-team/wangEditor/commit/a8bcd63d882832ac05a32878df0f767d145e0fa7))
* create editor ([12d98e4](https://github.com/wangeditor-team/wangEditor/commit/12d98e4bee179e9d277ec3ec2ecb827962ed0e75))
* create mode ([63c2eef](https://github.com/wangeditor-team/wangEditor/commit/63c2eef9a9a0a2838dfadd23483de35a76f88b0b))
* customPaste ([0f25f5c](https://github.com/wangeditor-team/wangEditor/commit/0f25f5cae3a2cd5ae5832f3fc1026b3ab6d047e0))
* divider menu ([5262634](https://github.com/wangeditor-team/wangEditor/commit/526263445616725541bf374b80260e73b1d4c6ec))
* drag resize image ([cd72028](https://github.com/wangeditor-team/wangEditor/commit/cd72028f1786e2e53079ad5cbef1b8569731ca79))
* editor 生命周期，自定义事件 ([00e9bc2](https://github.com/wangeditor-team/wangEditor/commit/00e9bc2cfcb8b622764db1c76394491d72ffd93e))
* editor with-selection plugin ([9f0a39f](https://github.com/wangeditor-team/wangEditor/commit/9f0a39fecf6d92888d2a97929820d3be038efb31))
* editor.isSelectedAll ([960c845](https://github.com/wangeditor-team/wangEditor/commit/960c8455f85a6bc7350f9944be80b3997bc1fea1))
* editor.showProgressBar ([51761d4](https://github.com/wangeditor-team/wangEditor/commit/51761d466ab3ef7c99e872954d4724ab51d8e28c))
* emotion ([736f955](https://github.com/wangeditor-team/wangEditor/commit/736f955211287bafca2375de3c8193cd0aa0856f))
* font-size + font-family ([cc649e0](https://github.com/wangeditor-team/wangEditor/commit/cc649e0918ce58e78b4d5ee49a400197b9d04b70))
* fullScreen ([e7ccd88](https://github.com/wangeditor-team/wangEditor/commit/e7ccd88a7dd58f64b7bd484de428e3a76cc994f7))
* getElemsByTypePrefix （删掉 getHeaders） ([c18834b](https://github.com/wangeditor-team/wangEditor/commit/c18834b3ebfd97fb36ccbe0faa84e6fe8c30eb67))
* header button menu ([6413135](https://github.com/wangeditor-team/wangEditor/commit/64131354d54705e11fd6992fcf5a4389371c3560))
* hover bar ([107356e](https://github.com/wangeditor-team/wangEditor/commit/107356eff7bfaf53ce25e39244f8133c80518375))
* i18n ([c11b244](https://github.com/wangeditor-team/wangEditor/commit/c11b2440f91b99d40bca18b675c66a22b6e160c9))
* image menu - width 50% 100% ([f9b4c68](https://github.com/wangeditor-team/wangEditor/commit/f9b4c68dff3232b50491b07949c20eb4c18baa6b))
* image menus & position ([bf5beba](https://github.com/wangeditor-team/wangEditor/commit/bf5beba7b3014d63f0b9fe0063530c8b101a5011))
* indent menu + groupMenu ([08db901](https://github.com/wangeditor-team/wangEditor/commit/08db901cd3a3f2ddb2173cc4b36d471e4e68237e))
* insert link ([b04242f](https://github.com/wangeditor-team/wangEditor/commit/b04242ffa252d4088f5360c3de45c24d6f493552))
* justify ([2ed7b88](https://github.com/wangeditor-team/wangEditor/commit/2ed7b883ca759dc4a9e0eefbd23cfe603a0f46fd))
* line-height menu ([755a752](https://github.com/wangeditor-team/wangEditor/commit/755a752d76803423f2794b85004d75055c9b54ec))
* list menu ([fe6c083](https://github.com/wangeditor-team/wangEditor/commit/fe6c0830b2c43e335e5972f85096f490694bbe19))
* menu color - part ([3a6cc86](https://github.com/wangeditor-team/wangEditor/commit/3a6cc86a7f9133d0862310c408abafb30c531734))
* menu color & dropPanel & menu config ([5d0d41b](https://github.com/wangeditor-team/wangEditor/commit/5d0d41b9a765a7deb583393f129925414c36ef35))
* modal appendTo body ([fc0ab06](https://github.com/wangeditor-team/wangEditor/commit/fc0ab06d5c7177eceb04643234a8c301ca4de396))
* parse html ([2a5eace](https://github.com/wangeditor-team/wangEditor/commit/2a5eace00f33cded50b68e8164748ec2480213fd))
* parse src (link image video) ([715a841](https://github.com/wangeditor-team/wangEditor/commit/715a841fc6c730ee2b448a1799a07ce778128aad))
* selectList ([b7366ab](https://github.com/wangeditor-team/wangEditor/commit/b7366ab2dafd379145d85881052d6f400bd13c85))
* shadow dom example ([c55f38d](https://github.com/wangeditor-team/wangEditor/commit/c55f38dab7886b9115c4353118d1f182885878ae))
* table module ([a397116](https://github.com/wangeditor-team/wangEditor/commit/a397116de73e088232d9c41828f30f8d56a22dd4))
* table module - header + fullWidth ([9a8a0e0](https://github.com/wangeditor-team/wangEditor/commit/9a8a0e093af944ee7deab674f47c2ec7baae0e63))
* text and toolbar ([3ae5d0c](https://github.com/wangeditor-team/wangEditor/commit/3ae5d0c4138fec7397ac8629e0012affe6b7dfa4))
* todo ([9608fef](https://github.com/wangeditor-team/wangEditor/commit/9608fef2ff86368cdcbb950a74af1246a58709de))
* toHtml 机制 ([1c4d872](https://github.com/wangeditor-team/wangEditor/commit/1c4d8729f84aaab6a448f23064b34a20596305e9))
* toolbar config - insertKeys ([a2f3c4b](https://github.com/wangeditor-team/wangEditor/commit/a2f3c4be3762831723495bbc9d50eb6c9b05d195))
* toolbar excludeKeys ([09bd196](https://github.com/wangeditor-team/wangEditor/commit/09bd196ea24c19b04e5e7e38227ca94332847bf8))
* tooltip ([994d875](https://github.com/wangeditor-team/wangEditor/commit/994d875fee81cf01271c2e440c1df202aa067d0e))
* undo redo - menu ([bfb3014](https://github.com/wangeditor-team/wangEditor/commit/bfb3014791cfcb2d7897f916bf280a57b1906e4d))
* updateLink + unLink + viewLink ([254d554](https://github.com/wangeditor-team/wangEditor/commit/254d55466b3c8527dd9f0bf34681abd801c8c8ce))
* upload image ([0a0564b](https://github.com/wangeditor-team/wangEditor/commit/0a0564bf14edd4dea6eb958e653272a9a216cec1))
* upload video ([ac8e6f8](https://github.com/wangeditor-team/wangEditor/commit/ac8e6f8b5258e593714676a6f6be359ba525833c))
* video menu ([c1faa1c](https://github.com/wangeditor-team/wangEditor/commit/c1faa1cfa896e1d240f5a2a100e1fd9b89dbef0b))
