{"_from": "@vueuse/core@^11.0.0", "_id": "@vueuse/core@11.3.0", "_inBundle": false, "_integrity": "sha512-7OC4Rl1f9G8IT6rUfi9JrKiXy4bfmHhZ5x2Ceojy0jnd3mHNEvV4JaRygH362ror6/NZ+Nl+n13LPzGiPN8cKA==", "_location": "/@vueuse/core", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@vueuse/core@^11.0.0", "name": "@vueuse/core", "escapedName": "@vueuse%2fcore", "scope": "@vueuse", "rawSpec": "^11.0.0", "saveSpec": null, "fetchSpec": "^11.0.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmmirror.com/@vueuse/core/-/core-11.3.0.tgz", "_shasum": "bb0bd1f0edd5435d20694dbe51091cf548653a4d", "_spec": "@vueuse/core@^11.0.0", "_where": "/Users/<USER>/Public/mklab/UI_Update_lab/art-design-pro-main", "author": {"name": "<PERSON>", "email": "https://github.com/antfu"}, "bugs": {"url": "https://github.com/vueuse/vueuse/issues"}, "bundleDependencies": false, "dependencies": {"@types/web-bluetooth": "^0.0.20", "@vueuse/metadata": "11.3.0", "@vueuse/shared": "11.3.0", "vue-demi": ">=0.14.10"}, "deprecated": false, "description": "Collection of essential Vue Composition Utilities", "exports": {".": {"import": "./index.mjs", "require": "./index.cjs"}, "./*": "./*", "./metadata": {"import": "./metadata.mjs", "require": "./metadata.cjs"}}, "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/vueuse/vueuse#readme", "jsdelivr": "./index.iife.min.js", "keywords": ["vue", "vue-use", "utils"], "license": "MIT", "main": "./index.cjs", "module": "./index.mjs", "name": "@vueuse/core", "repository": {"type": "git", "url": "git+https://github.com/vueuse/vueuse.git", "directory": "packages/core"}, "sideEffects": false, "types": "./index.d.cts", "unpkg": "./index.iife.min.js", "version": "11.3.0"}