var _VueDemiGlobal=typeof globalThis<"u"?globalThis:typeof global<"u"?global:typeof self<"u"?self:this,VueDemi=function(S,d,r){if(S.install)return S;if(!d)return console.error("[vue-demi] no Vue instance found, please be sure to import `vue` before `vue-demi`."),S;if(d.version.slice(0,4)==="2.7."){let z=function(X,J){var K,I={},B={config:d.config,use:d.use.bind(d),mixin:d.mixin.bind(d),component:d.component.bind(d),provide:function(H,Q){return I[H]=Q,this},directive:function(H,Q){return Q?(d.directive(H,Q),B):d.directive(H)},mount:function(H,Q){return K||(K=new d(Object.assign({propsData:J},X,{provide:Object.assign(I,X.provide)})),K.$mount(H,Q),K)},unmount:function(){K&&(K.$destroy(),K=void 0)}};return B};var Ae=z;for(var Y in d)S[Y]=d[Y];S.isVue2=!0,S.isVue3=!1,S.install=function(){},S.Vue=d,S.Vue2=d,S.version=d.version,S.warn=d.util.warn,S.hasInjectionContext=function(){return!!S.getCurrentInstance()},S.createApp=z}else if(d.version.slice(0,2)==="2.")if(r){for(var Y in r)S[Y]=r[Y];S.isVue2=!0,S.isVue3=!1,S.install=function(){},S.Vue=d,S.Vue2=d,S.version=d.version,S.hasInjectionContext=function(){return!!S.getCurrentInstance()}}else console.error("[vue-demi] no VueCompositionAPI instance found, please be sure to import `@vue/composition-api` before `vue-demi`.");else if(d.version.slice(0,2)==="3."){for(var Y in d)S[Y]=d[Y];S.isVue2=!1,S.isVue3=!0,S.install=function(){},S.Vue=d,S.Vue2=void 0,S.version=d.version,S.set=function(z,X,J){return Array.isArray(z)?(z.length=Math.max(z.length,X),z.splice(X,1,J),J):(z[X]=J,J)},S.del=function(z,X){if(Array.isArray(z)){z.splice(X,1);return}delete z[X]}}else console.error("[vue-demi] Vue version "+d.version+" is unsupported.");return S}(_VueDemiGlobal.VueDemi=_VueDemiGlobal.VueDemi||(typeof VueDemi<"u"?VueDemi:{}),_VueDemiGlobal.Vue||(typeof Vue<"u"?Vue:void 0),_VueDemiGlobal.VueCompositionAPI||(typeof VueCompositionAPI<"u"?VueCompositionAPI:void 0));(function(S,d,r){"use strict";function Y(e,t,n){let o;r.isRef(n)?o={evaluating:n}:o=n||{};const{lazy:a=!1,evaluating:l=void 0,shallow:u=!0,onError:s=d.noop}=o,i=r.ref(!a),f=u?r.shallowRef(t):r.ref(t);let c=0;return r.watchEffect(async v=>{if(!i.value)return;c++;const g=c;let p=!1;l&&Promise.resolve().then(()=>{l.value=!0});try{const b=await e(y=>{v(()=>{l&&(l.value=!1),p||y()})});g===c&&(f.value=b)}catch(b){s(b)}finally{l&&g===c&&(l.value=!1),p=!0}}),a?r.computed(()=>(i.value=!0,f.value)):f}function Ae(e,t,n,o){let a=r.inject(e);return n&&(a=r.inject(e,n)),o&&(a=r.inject(e,n,o)),typeof t=="function"?r.computed(l=>t(a,l)):r.computed({get:l=>t.get(a,l),set:t.set})}function z(e={}){if(!r.isVue3&&!r.version.startsWith("2.7.")){if(process.env.NODE_ENV!=="production")throw new Error("[VueUse] createReusableTemplate only works in Vue 2.7 or above.");return}const{inheritAttrs:t=!0}=e,n=r.shallowRef(),o=r.defineComponent({setup(l,{slots:u}){return()=>{n.value=u.default}}}),a=r.defineComponent({inheritAttrs:t,setup(l,{attrs:u,slots:s}){return()=>{var i;if(!n.value&&process.env.NODE_ENV!=="production")throw new Error("[VueUse] Failed to find the definition of reusable template");const f=(i=n.value)==null?void 0:i.call(n,{...X(u),$slots:s});return t&&f?.length===1?f[0]:f}}});return d.makeDestructurable({define:o,reuse:a},[o,a])}function X(e){const t={};for(const n in e)t[d.camelize(n)]=e[n];return t}function J(e={}){if(!r.isVue3){if(process.env.NODE_ENV!=="production")throw new Error("[VueUse] createTemplatePromise only works in Vue 3 or above.");return}let t=0;const n=r.ref([]);function o(...u){const s=r.shallowReactive({key:t++,args:u,promise:void 0,resolve:()=>{},reject:()=>{},isResolving:!1,options:e});return n.value.push(s),s.promise=new Promise((i,f)=>{s.resolve=c=>(s.isResolving=!0,i(c)),s.reject=f}).finally(()=>{s.promise=void 0;const i=n.value.indexOf(s);i!==-1&&n.value.splice(i,1)}),s.promise}function a(...u){return e.singleton&&n.value.length>0?n.value[0].promise:o(...u)}const l=r.defineComponent((u,{slots:s})=>{const i=()=>n.value.map(f=>{var c;return r.h(r.Fragment,{key:f.key},(c=s.default)==null?void 0:c.call(s,f))});return e.transition?()=>r.h(r.TransitionGroup,e.transition,i):i});return l.start=a,l}function K(e){return function(...t){return e.apply(this,t.map(n=>d.toValue(n)))}}const I=d.isClient?window:void 0,B=d.isClient?window.document:void 0,H=d.isClient?window.navigator:void 0,Q=d.isClient?window.location:void 0;function N(e){var t;const n=d.toValue(e);return(t=n?.$el)!=null?t:n}function O(...e){let t,n,o,a;if(typeof e[0]=="string"||Array.isArray(e[0])?([n,o,a]=e,t=I):[t,n,o,a]=e,!t)return d.noop;Array.isArray(n)||(n=[n]),Array.isArray(o)||(o=[o]);const l=[],u=()=>{l.forEach(c=>c()),l.length=0},s=(c,v,g,p)=>(c.addEventListener(v,g,p),()=>c.removeEventListener(v,g,p)),i=r.watch(()=>[N(t),d.toValue(a)],([c,v])=>{if(u(),!c)return;const g=d.isObject(v)?{...v}:v;l.push(...n.flatMap(p=>o.map(b=>s(c,p,b,g))))},{immediate:!0,flush:"post"}),f=()=>{i(),u()};return d.tryOnScopeDispose(f),f}let Ie=!1;function Ft(e,t,n={}){const{window:o=I,ignore:a=[],capture:l=!0,detectIframe:u=!1}=n;if(!o)return d.noop;d.isIOS&&!Ie&&(Ie=!0,Array.from(o.document.body.children).forEach(y=>y.addEventListener("click",d.noop)),o.document.documentElement.addEventListener("click",d.noop));let s=!0;const i=y=>d.toValue(a).some(h=>{if(typeof h=="string")return Array.from(o.document.querySelectorAll(h)).some(m=>m===y.target||y.composedPath().includes(m));{const m=N(h);return m&&(y.target===m||y.composedPath().includes(m))}});function f(y){const h=d.toValue(y);return h&&h.$.subTree.shapeFlag===16}function c(y,h){const m=d.toValue(y),w=m.$.subTree&&m.$.subTree.children;return w==null||!Array.isArray(w)?!1:w.some(E=>E.el===h.target||h.composedPath().includes(E.el))}const v=y=>{const h=N(e);if(y.target!=null&&!(!(h instanceof Element)&&f(e)&&c(e,y))&&!(!h||h===y.target||y.composedPath().includes(h))){if(y.detail===0&&(s=!i(y)),!s){s=!0;return}t(y)}};let g=!1;const p=[O(o,"click",y=>{g||(g=!0,setTimeout(()=>{g=!1},0),v(y))},{passive:!0,capture:l}),O(o,"pointerdown",y=>{const h=N(e);s=!i(y)&&!!(h&&!y.composedPath().includes(h))},{passive:!0}),u&&O(o,"blur",y=>{setTimeout(()=>{var h;const m=N(e);((h=o.document.activeElement)==null?void 0:h.tagName)==="IFRAME"&&!m?.contains(o.document.activeElement)&&t(y)},0)})].filter(Boolean);return()=>p.forEach(y=>y())}function Pt(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function fe(...e){let t,n,o={};e.length===3?(t=e[0],n=e[1],o=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,n=e[0],o=e[1]):(t=e[0],n=e[1]):(t=!0,n=e[0]);const{target:a=I,eventName:l="keydown",passive:u=!1,dedupe:s=!1}=o,i=Pt(t);return O(a,l,c=>{c.repeat&&d.toValue(s)||i(c)&&n(c)},u)}function Ct(e,t,n={}){return fe(e,t,{...n,eventName:"keydown"})}function Vt(e,t,n={}){return fe(e,t,{...n,eventName:"keypress"})}function At(e,t,n={}){return fe(e,t,{...n,eventName:"keyup"})}const It=500,Mt=10;function Lt(e,t,n){var o,a;const l=r.computed(()=>N(e));let u,s,i,f=!1;function c(){u&&(clearTimeout(u),u=void 0),s=void 0,i=void 0,f=!1}function v(m){var w,E,k;const[_,A,T]=[i,s,f];if(c(),!n?.onMouseUp||!A||!_||(w=n?.modifiers)!=null&&w.self&&m.target!==l.value)return;(E=n?.modifiers)!=null&&E.prevent&&m.preventDefault(),(k=n?.modifiers)!=null&&k.stop&&m.stopPropagation();const F=m.x-A.x,C=m.y-A.y,P=Math.sqrt(F*F+C*C);n.onMouseUp(m.timeStamp-_,P,T)}function g(m){var w,E,k,_;(w=n?.modifiers)!=null&&w.self&&m.target!==l.value||(c(),(E=n?.modifiers)!=null&&E.prevent&&m.preventDefault(),(k=n?.modifiers)!=null&&k.stop&&m.stopPropagation(),s={x:m.x,y:m.y},i=m.timeStamp,u=setTimeout(()=>{f=!0,t(m)},(_=n?.delay)!=null?_:It))}function p(m){var w,E,k,_;if((w=n?.modifiers)!=null&&w.self&&m.target!==l.value||!s||n?.distanceThreshold===!1)return;(E=n?.modifiers)!=null&&E.prevent&&m.preventDefault(),(k=n?.modifiers)!=null&&k.stop&&m.stopPropagation();const A=m.x-s.x,T=m.y-s.y;Math.sqrt(A*A+T*T)>=((_=n?.distanceThreshold)!=null?_:Mt)&&c()}const b={capture:(o=n?.modifiers)==null?void 0:o.capture,once:(a=n?.modifiers)==null?void 0:a.once},y=[O(l,"pointerdown",g,b),O(l,"pointermove",p,b),O(l,["pointerup","pointerleave"],v,b)];return()=>y.forEach(m=>m())}function Nt(){const{activeElement:e,body:t}=document;if(!e||e===t)return!1;switch(e.tagName){case"INPUT":case"TEXTAREA":return!0}return e.hasAttribute("contenteditable")}function xt({keyCode:e,metaKey:t,ctrlKey:n,altKey:o}){return t||n||o?!1:e>=48&&e<=57||e>=65&&e<=90||e>=97&&e<=122}function $t(e,t={}){const{document:n=B}=t;n&&O(n,"keydown",a=>{!Nt()&&xt(a)&&e(a)},{passive:!0})}function Wt(e,t=null){const n=r.getCurrentInstance();let o=()=>{};const a=r.customRef((l,u)=>(o=u,{get(){var s,i;return l(),(i=(s=n?.proxy)==null?void 0:s.$refs[e])!=null?i:t},set(){}}));return d.tryOnMounted(o),r.onUpdated(o),a}function Me(){const e=r.ref(!1),t=r.getCurrentInstance();return t&&r.onMounted(()=>{e.value=!0},r.isVue2?void 0:t),e}function x(e){const t=Me();return r.computed(()=>(t.value,!!e()))}function oe(e,t,n={}){const{window:o=I,...a}=n;let l;const u=x(()=>o&&"MutationObserver"in o),s=()=>{l&&(l.disconnect(),l=void 0)},i=r.computed(()=>{const g=d.toValue(e),p=(Array.isArray(g)?g:[g]).map(N).filter(d.notNullish);return new Set(p)}),f=r.watch(()=>i.value,g=>{s(),u.value&&g.size&&(l=new MutationObserver(t),g.forEach(p=>l.observe(p,a)))},{immediate:!0,flush:"post"}),c=()=>l?.takeRecords(),v=()=>{f(),s()};return d.tryOnScopeDispose(v),{isSupported:u,stop:v,takeRecords:c}}function Le(e={}){var t;const{window:n=I,deep:o=!0,triggerOnRemoval:a=!1}=e,l=(t=e.document)!=null?t:n?.document,u=()=>{var f;let c=l?.activeElement;if(o)for(;c?.shadowRoot;)c=(f=c?.shadowRoot)==null?void 0:f.activeElement;return c},s=r.ref(),i=()=>{s.value=u()};return n&&(O(n,"blur",f=>{f.relatedTarget===null&&i()},!0),O(n,"focus",i,!0)),a&&oe(l,f=>{f.filter(c=>c.removedNodes.length).map(c=>Array.from(c.removedNodes)).flat().forEach(c=>{c===s.value&&i()})},{childList:!0,subtree:!0}),i(),s}function ee(e,t={}){const{immediate:n=!0,fpsLimit:o=void 0,window:a=I}=t,l=r.ref(!1),u=o?1e3/o:null;let s=0,i=null;function f(g){if(!l.value||!a)return;s||(s=g);const p=g-s;if(u&&p<u){i=a.requestAnimationFrame(f);return}s=g,e({delta:p,timestamp:g}),i=a.requestAnimationFrame(f)}function c(){!l.value&&a&&(l.value=!0,s=0,i=a.requestAnimationFrame(f))}function v(){l.value=!1,i!=null&&a&&(a.cancelAnimationFrame(i),i=null)}return n&&c(),d.tryOnScopeDispose(v),{isActive:r.readonly(l),pause:v,resume:c}}function Ut(e,t,n){let o,a;d.isObject(n)?(o=n,a=d.objectOmit(n,["window","immediate","commitStyles","persist","onReady","onError"])):(o={duration:n},a=n);const{window:l=I,immediate:u=!0,commitStyles:s,persist:i,playbackRate:f=1,onReady:c,onError:v=M=>{console.error(M)}}=o,g=x(()=>l&&HTMLElement&&"animate"in HTMLElement.prototype),p=r.shallowRef(void 0),b=r.shallowReactive({startTime:null,currentTime:null,timeline:null,playbackRate:f,pending:!1,playState:u?"idle":"paused",replaceState:"active"}),y=r.computed(()=>b.pending),h=r.computed(()=>b.playState),m=r.computed(()=>b.replaceState),w=r.computed({get(){return b.startTime},set(M){b.startTime=M,p.value&&(p.value.startTime=M)}}),E=r.computed({get(){return b.currentTime},set(M){b.currentTime=M,p.value&&(p.value.currentTime=M,$())}}),k=r.computed({get(){return b.timeline},set(M){b.timeline=M,p.value&&(p.value.timeline=M)}}),_=r.computed({get(){return b.playbackRate},set(M){b.playbackRate=M,p.value&&(p.value.playbackRate=M)}}),A=()=>{if(p.value)try{p.value.play(),$()}catch(M){U(),v(M)}else R()},T=()=>{var M;try{(M=p.value)==null||M.pause(),U()}catch(W){v(W)}},F=()=>{var M;p.value||R();try{(M=p.value)==null||M.reverse(),$()}catch(W){U(),v(W)}},C=()=>{var M;try{(M=p.value)==null||M.finish(),U()}catch(W){v(W)}},P=()=>{var M;try{(M=p.value)==null||M.cancel(),U()}catch(W){v(W)}};r.watch(()=>N(e),M=>{M&&R()}),r.watch(()=>t,M=>{p.value&&R(),!N(e)&&p.value&&(p.value.effect=new KeyframeEffect(N(e),d.toValue(M),a))},{deep:!0}),d.tryOnMounted(()=>R(!0),!1),d.tryOnScopeDispose(P);function R(M){const W=N(e);!g.value||!W||(p.value||(p.value=W.animate(d.toValue(t),a)),i&&p.value.persist(),f!==1&&(p.value.playbackRate=f),M&&!u?p.value.pause():$(),c?.(p.value))}O(p,["cancel","finish","remove"],U),O(p,"finish",()=>{var M;s&&((M=p.value)==null||M.commitStyles())});const{resume:V,pause:L}=ee(()=>{p.value&&(b.pending=p.value.pending,b.playState=p.value.playState,b.replaceState=p.value.replaceState,b.startTime=p.value.startTime,b.currentTime=p.value.currentTime,b.timeline=p.value.timeline,b.playbackRate=p.value.playbackRate)},{immediate:!1});function $(){g.value&&V()}function U(){g.value&&l&&l.requestAnimationFrame(L)}return{isSupported:g,animate:p,play:A,pause:T,reverse:F,finish:C,cancel:P,pending:y,playState:h,replaceState:m,startTime:w,currentTime:E,timeline:k,playbackRate:_}}function Ht(e,t){const{interrupt:n=!0,onError:o=d.noop,onFinished:a=d.noop,signal:l}=t||{},u={aborted:"aborted",fulfilled:"fulfilled",pending:"pending",rejected:"rejected"},s=Array.from(Array.from({length:e.length}),()=>({state:u.pending,data:null})),i=r.reactive(s),f=r.ref(-1);if(!e||e.length===0)return a(),{activeIndex:f,result:i};function c(v,g){f.value++,i[f.value].data=g,i[f.value].state=v}return e.reduce((v,g)=>v.then(p=>{var b;if(l?.aborted){c(u.aborted,new Error("aborted"));return}if(((b=i[f.value])==null?void 0:b.state)===u.rejected&&n){a();return}const y=g(p).then(h=>(c(u.fulfilled,h),f.value===e.length-1&&a(),h));return l?Promise.race([y,Bt(l)]):y}).catch(p=>l?.aborted?(c(u.aborted,p),p):(c(u.rejected,p),o(),p)),Promise.resolve()),{activeIndex:f,result:i}}function Bt(e){return new Promise((t,n)=>{const o=new Error("aborted");e.aborted?n(o):e.addEventListener("abort",()=>n(o),{once:!0})})}function Ne(e,t,n){const{immediate:o=!0,delay:a=0,onError:l=d.noop,onSuccess:u=d.noop,resetOnExecute:s=!0,shallow:i=!0,throwError:f}=n??{},c=i?r.shallowRef(t):r.ref(t),v=r.ref(!1),g=r.ref(!1),p=r.shallowRef(void 0);async function b(m=0,...w){s&&(c.value=t),p.value=void 0,v.value=!1,g.value=!0,m>0&&await d.promiseTimeout(m);const E=typeof e=="function"?e(...w):e;try{const k=await E;c.value=k,v.value=!0,u(k)}catch(k){if(p.value=k,l(k),f)throw k}finally{g.value=!1}return c.value}o&&b(a);const y={state:c,isReady:v,isLoading:g,error:p,execute:b};function h(){return new Promise((m,w)=>{d.until(g).toBe(!1).then(()=>m(y)).catch(w)})}return{...y,then(m,w){return h().then(m,w)}}}const re={array:e=>JSON.stringify(e),object:e=>JSON.stringify(e),set:e=>JSON.stringify(Array.from(e)),map:e=>JSON.stringify(Object.fromEntries(e)),null:()=>""};function jt(e){return e?e instanceof Map?re.map:e instanceof Set?re.set:Array.isArray(e)?re.array:re.object:re.null}function zt(e,t){const n=r.ref(""),o=r.ref();function a(){if(d.isClient)return o.value=new Promise((l,u)=>{try{const s=d.toValue(e);if(s==null)l("");else if(typeof s=="string")l(Ee(new Blob([s],{type:"text/plain"})));else if(s instanceof Blob)l(Ee(s));else if(s instanceof ArrayBuffer)l(window.btoa(String.fromCharCode(...new Uint8Array(s))));else if(s instanceof HTMLCanvasElement)l(s.toDataURL(t?.type,t?.quality));else if(s instanceof HTMLImageElement){const i=s.cloneNode(!1);i.crossOrigin="Anonymous",qt(i).then(()=>{const f=document.createElement("canvas"),c=f.getContext("2d");f.width=i.width,f.height=i.height,c.drawImage(i,0,0,f.width,f.height),l(f.toDataURL(t?.type,t?.quality))}).catch(u)}else if(typeof s=="object"){const f=(t?.serializer||jt(s))(s);return l(Ee(new Blob([f],{type:"application/json"})))}else u(new Error("target is unsupported types"))}catch(s){u(s)}}),o.value.then(l=>n.value=l),o.value}return r.isRef(e)||typeof e=="function"?r.watch(e,a,{immediate:!0}):a(),{base64:n,promise:o,execute:a}}function qt(e){return new Promise((t,n)=>{e.complete?t():(e.onload=()=>{t()},e.onerror=n)})}function Ee(e){return new Promise((t,n)=>{const o=new FileReader;o.onload=a=>{t(a.target.result)},o.onerror=n,o.readAsDataURL(e)})}function Gt(e={}){const{navigator:t=H}=e,n=["chargingchange","chargingtimechange","dischargingtimechange","levelchange"],o=x(()=>t&&"getBattery"in t&&typeof t.getBattery=="function"),a=r.ref(!1),l=r.ref(0),u=r.ref(0),s=r.ref(1);let i;function f(){a.value=this.charging,l.value=this.chargingTime||0,u.value=this.dischargingTime||0,s.value=this.level}return o.value&&t.getBattery().then(c=>{i=c,f.call(i),O(i,n,f,{passive:!0})}),{isSupported:o,charging:a,chargingTime:l,dischargingTime:u,level:s}}function Yt(e){let{acceptAllDevices:t=!1}=e||{};const{filters:n=void 0,optionalServices:o=void 0,navigator:a=H}=e||{},l=x(()=>a&&"bluetooth"in a),u=r.shallowRef(void 0),s=r.shallowRef(null);r.watch(u,()=>{v()});async function i(){if(l.value){s.value=null,n&&n.length>0&&(t=!1);try{u.value=await a?.bluetooth.requestDevice({acceptAllDevices:t,filters:n,optionalServices:o})}catch(g){s.value=g}}}const f=r.ref(),c=r.computed(()=>{var g;return((g=f.value)==null?void 0:g.connected)||!1});async function v(){if(s.value=null,u.value&&u.value.gatt){u.value.addEventListener("gattserverdisconnected",()=>{});try{f.value=await u.value.gatt.connect()}catch(g){s.value=g}}}return d.tryOnMounted(()=>{var g;u.value&&((g=u.value.gatt)==null||g.connect())}),d.tryOnScopeDispose(()=>{var g;u.value&&((g=u.value.gatt)==null||g.disconnect())}),{isSupported:l,isConnected:c,device:u,requestDevice:i,server:f,error:s}}function q(e,t={}){const{window:n=I}=t,o=x(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function");let a;const l=r.ref(!1),u=f=>{l.value=f.matches},s=()=>{a&&("removeEventListener"in a?a.removeEventListener("change",u):a.removeListener(u))},i=r.watchEffect(()=>{o.value&&(s(),a=n.matchMedia(d.toValue(e)),"addEventListener"in a?a.addEventListener("change",u):a.addListener(u),l.value=a.matches)});return d.tryOnScopeDispose(()=>{i(),s(),a=void 0}),l}const Xt={sm:640,md:768,lg:1024,xl:1280,"2xl":1536},Kt={xs:0,sm:576,md:768,lg:992,xl:1200,xxl:1400},xe={xs:0,sm:600,md:960,lg:1264,xl:1904},Jt={xs:0,sm:600,md:960,lg:1280,xl:1920,xxl:2560},Qt=xe,Zt={xs:480,sm:576,md:768,lg:992,xl:1200,xxl:1600},Dt={xs:0,sm:600,md:1024,lg:1440,xl:1920},en={mobileS:320,mobileM:375,mobileL:425,tablet:768,laptop:1024,laptopL:1440,desktop4K:2560},tn={"3xs":360,"2xs":480,xs:600,sm:768,md:1024,lg:1280,xl:1440,"2xl":1600,"3xl":1920,"4xl":2560},nn={sm:576,md:768,lg:992,xl:1200},on={xs:0,sm:768,md:992,lg:1200,xl:1920};function ln(e,t={}){function n(c,v){let g=d.toValue(e[d.toValue(c)]);return v!=null&&(g=d.increaseWithUnit(g,v)),typeof g=="number"&&(g=`${g}px`),g}const{window:o=I,strategy:a="min-width"}=t;function l(c){return o?o.matchMedia(c).matches:!1}const u=c=>q(()=>`(min-width: ${n(c)})`,t),s=c=>q(()=>`(max-width: ${n(c)})`,t),i=Object.keys(e).reduce((c,v)=>(Object.defineProperty(c,v,{get:()=>a==="min-width"?u(v):s(v),enumerable:!0,configurable:!0}),c),{});function f(){const c=Object.keys(e).map(v=>[v,u(v)]);return r.computed(()=>c.filter(([,v])=>v.value).map(([v])=>v))}return Object.assign(i,{greaterOrEqual:u,smallerOrEqual:s,greater(c){return q(()=>`(min-width: ${n(c,.1)})`,t)},smaller(c){return q(()=>`(max-width: ${n(c,-.1)})`,t)},between(c,v){return q(()=>`(min-width: ${n(c)}) and (max-width: ${n(v,-.1)})`,t)},isGreater(c){return l(`(min-width: ${n(c,.1)})`)},isGreaterOrEqual(c){return l(`(min-width: ${n(c)})`)},isSmaller(c){return l(`(max-width: ${n(c,-.1)})`)},isSmallerOrEqual(c){return l(`(max-width: ${n(c)})`)},isInBetween(c,v){return l(`(min-width: ${n(c)}) and (max-width: ${n(v,-.1)})`)},current:f,active(){const c=f();return r.computed(()=>c.value.length===0?"":c.value.at(-1))}})}function rn(e){const{name:t,window:n=I}=e,o=x(()=>n&&"BroadcastChannel"in n),a=r.ref(!1),l=r.ref(),u=r.ref(),s=r.shallowRef(null),i=c=>{l.value&&l.value.postMessage(c)},f=()=>{l.value&&l.value.close(),a.value=!0};return o.value&&d.tryOnMounted(()=>{s.value=null,l.value=new BroadcastChannel(t),l.value.addEventListener("message",c=>{u.value=c.data},{passive:!0}),l.value.addEventListener("messageerror",c=>{s.value=c},{passive:!0}),l.value.addEventListener("close",()=>{a.value=!0})}),d.tryOnScopeDispose(()=>{f()}),{isSupported:o,channel:l,data:u,post:i,close:f,error:s,isClosed:a}}const $e=["hash","host","hostname","href","pathname","port","protocol","search"];function an(e={}){const{window:t=I}=e,n=Object.fromEntries($e.map(l=>[l,r.ref()]));for(const[l,u]of d.objectEntries(n))r.watch(u,s=>{!t?.location||t.location[l]===s||(t.location[l]=s)});const o=l=>{var u;const{state:s,length:i}=t?.history||{},{origin:f}=t?.location||{};for(const c of $e)n[c].value=(u=t?.location)==null?void 0:u[c];return r.reactive({trigger:l,state:s,length:i,origin:f,...n})},a=r.ref(o("load"));return t&&(O(t,"popstate",()=>a.value=o("popstate"),{passive:!0}),O(t,"hashchange",()=>a.value=o("hashchange"),{passive:!0})),a}function un(e,t=(o,a)=>o===a,n){const o=r.ref(e.value);return r.watch(()=>e.value,a=>{t(a,o.value)||(o.value=a)},n),o}function de(e,t={}){const{controls:n=!1,navigator:o=H}=t,a=x(()=>o&&"permissions"in o),l=r.shallowRef(),u=typeof e=="string"?{name:e}:e,s=r.shallowRef(),i=()=>{var c,v;s.value=(v=(c=l.value)==null?void 0:c.state)!=null?v:"prompt"};O(l,"change",i);const f=d.createSingletonPromise(async()=>{if(a.value){if(!l.value)try{l.value=await o.permissions.query(u)}catch{l.value=void 0}finally{i()}if(n)return r.toRaw(l.value)}});return f(),n?{state:s,isSupported:a,query:f}:s}function sn(e={}){const{navigator:t=H,read:n=!1,source:o,copiedDuring:a=1500,legacy:l=!1}=e,u=x(()=>t&&"clipboard"in t),s=de("clipboard-read"),i=de("clipboard-write"),f=r.computed(()=>u.value||l),c=r.ref(""),v=r.ref(!1),g=d.useTimeoutFn(()=>v.value=!1,a);function p(){u.value&&m(s.value)?t.clipboard.readText().then(w=>{c.value=w}):c.value=h()}f.value&&n&&O(["copy","cut"],p);async function b(w=d.toValue(o)){f.value&&w!=null&&(u.value&&m(i.value)?await t.clipboard.writeText(w):y(w),c.value=w,v.value=!0,g.start())}function y(w){const E=document.createElement("textarea");E.value=w??"",E.style.position="absolute",E.style.opacity="0",document.body.appendChild(E),E.select(),document.execCommand("copy"),E.remove()}function h(){var w,E,k;return(k=(E=(w=document?.getSelection)==null?void 0:w.call(document))==null?void 0:E.toString())!=null?k:""}function m(w){return w==="granted"||w==="prompt"}return{isSupported:f,text:c,copied:v,copy:b}}function cn(e={}){const{navigator:t=H,read:n=!1,source:o,copiedDuring:a=1500}=e,l=x(()=>t&&"clipboard"in t),u=r.ref([]),s=r.ref(!1),i=d.useTimeoutFn(()=>s.value=!1,a);function f(){l.value&&t.clipboard.read().then(v=>{u.value=v})}l.value&&n&&O(["copy","cut"],f);async function c(v=d.toValue(o)){l.value&&v!=null&&(await t.clipboard.write(v),u.value=v,s.value=!0,i.start())}return{isSupported:l,content:u,copied:s,copy:c}}function ae(e){return JSON.parse(JSON.stringify(e))}function fn(e,t={}){const n=r.ref({}),{manual:o,clone:a=ae,deep:l=!0,immediate:u=!0}=t;function s(){n.value=a(d.toValue(e))}return!o&&(r.isRef(e)||typeof e=="function")?r.watch(e,s,{...t,deep:l,immediate:u}):s(),{cloned:n,sync:s}}const ve=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},pe="__vueuse_ssr_handlers__",We=dn();function dn(){return pe in ve||(ve[pe]=ve[pe]||{}),ve[pe]}function ye(e,t){return We[e]||t}function vn(e,t){We[e]=t}function Te(e){return q("(prefers-color-scheme: dark)",e)}function Ue(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const ke={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},Oe="vueuse-storage";function me(e,t,n,o={}){var a;const{flush:l="pre",deep:u=!0,listenToStorageChanges:s=!0,writeDefaults:i=!0,mergeDefaults:f=!1,shallow:c,window:v=I,eventFilter:g,onError:p=P=>{console.error(P)},initOnMounted:b}=o,y=(c?r.shallowRef:r.ref)(typeof t=="function"?t():t);if(!n)try{n=ye("getDefaultStorage",()=>{var P;return(P=I)==null?void 0:P.localStorage})()}catch(P){p(P)}if(!n)return y;const h=d.toValue(t),m=Ue(h),w=(a=o.serializer)!=null?a:ke[m],{pause:E,resume:k}=d.pausableWatch(y,()=>A(y.value),{flush:l,deep:u,eventFilter:g});v&&s&&d.tryOnMounted(()=>{n instanceof Storage?O(v,"storage",F):O(v,Oe,C),b&&F()}),b||F();function _(P,R){if(v){const V={key:e,oldValue:P,newValue:R,storageArea:n};v.dispatchEvent(n instanceof Storage?new StorageEvent("storage",V):new CustomEvent(Oe,{detail:V}))}}function A(P){try{const R=n.getItem(e);if(P==null)_(R,null),n.removeItem(e);else{const V=w.write(P);R!==V&&(n.setItem(e,V),_(R,V))}}catch(R){p(R)}}function T(P){const R=P?P.newValue:n.getItem(e);if(R==null)return i&&h!=null&&n.setItem(e,w.write(h)),h;if(!P&&f){const V=w.read(R);return typeof f=="function"?f(V,h):m==="object"&&!Array.isArray(V)?{...h,...V}:V}else return typeof R!="string"?R:w.read(R)}function F(P){if(!(P&&P.storageArea!==n)){if(P&&P.key==null){y.value=h;return}if(!(P&&P.key!==e)){E();try{P?.newValue!==w.write(y.value)&&(y.value=T(P))}catch(R){p(R)}finally{P?r.nextTick(k):k()}}}}function C(P){F(P.detail)}return y}const pn="*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}";function He(e={}){const{selector:t="html",attribute:n="class",initialValue:o="auto",window:a=I,storage:l,storageKey:u="vueuse-color-scheme",listenToStorageChanges:s=!0,storageRef:i,emitAuto:f,disableTransition:c=!0}=e,v={auto:"",light:"light",dark:"dark",...e.modes||{}},g=Te({window:a}),p=r.computed(()=>g.value?"dark":"light"),b=i||(u==null?d.toRef(o):me(u,o,l,{window:a,listenToStorageChanges:s})),y=r.computed(()=>b.value==="auto"?p.value:b.value),h=ye("updateHTMLAttrs",(k,_,A)=>{const T=typeof k=="string"?a?.document.querySelector(k):N(k);if(!T)return;const F=new Set,C=new Set;let P=null;if(_==="class"){const V=A.split(/\s/g);Object.values(v).flatMap(L=>(L||"").split(/\s/g)).filter(Boolean).forEach(L=>{V.includes(L)?F.add(L):C.add(L)})}else P={key:_,value:A};if(F.size===0&&C.size===0&&P===null)return;let R;c&&(R=a.document.createElement("style"),R.appendChild(document.createTextNode(pn)),a.document.head.appendChild(R));for(const V of F)T.classList.add(V);for(const V of C)T.classList.remove(V);P&&T.setAttribute(P.key,P.value),c&&(a.getComputedStyle(R).opacity,document.head.removeChild(R))});function m(k){var _;h(t,n,(_=v[k])!=null?_:k)}function w(k){e.onChanged?e.onChanged(k,m):m(k)}r.watch(y,w,{flush:"post",immediate:!0}),d.tryOnMounted(()=>w(y.value));const E=r.computed({get(){return f?b.value:y.value},set(k){b.value=k}});try{return Object.assign(E,{store:b,system:p,state:y})}catch{return E}}function yn(e=r.ref(!1)){const t=d.createEventHook(),n=d.createEventHook(),o=d.createEventHook();let a=d.noop;const l=i=>(o.trigger(i),e.value=!0,new Promise(f=>{a=f})),u=i=>{e.value=!1,t.trigger(i),a({data:i,isCanceled:!1})},s=i=>{e.value=!1,n.trigger(i),a({data:i,isCanceled:!0})};return{isRevealed:r.computed(()=>e.value),reveal:l,confirm:u,cancel:s,onReveal:o.on,onConfirm:t.on,onCancel:n.on}}function ue(e,t,n={}){const{window:o=I,initialValue:a,observe:l=!1}=n,u=r.ref(a),s=r.computed(()=>{var f;return N(t)||((f=o?.document)==null?void 0:f.documentElement)});function i(){var f;const c=d.toValue(e),v=d.toValue(s);if(v&&o&&c){const g=(f=o.getComputedStyle(v).getPropertyValue(c))==null?void 0:f.trim();u.value=g||a}}return l&&oe(s,i,{attributeFilter:["style","class"],window:o}),r.watch([s,()=>d.toValue(e)],(f,c)=>{c[0]&&c[1]&&c[0].style.removeProperty(c[1]),i()},{immediate:!0}),r.watch(u,f=>{var c;const v=d.toValue(e);(c=s.value)!=null&&c.style&&v&&(f==null?s.value.style.removeProperty(v):s.value.style.setProperty(v,f))}),u}function Be(e){const t=r.getCurrentInstance(),n=d.computedWithControl(()=>null,()=>e?N(e):t.proxy.$el);return r.onUpdated(n.trigger),r.onMounted(n.trigger),n}function mn(e,t){const n=r.shallowRef(f()),o=d.toRef(e),a=r.computed({get(){var c;const v=o.value;let g=t?.getIndexOf?t.getIndexOf(n.value,v):v.indexOf(n.value);return g<0&&(g=(c=t?.fallbackIndex)!=null?c:0),g},set(c){l(c)}});function l(c){const v=o.value,g=v.length,p=(c%g+g)%g,b=v[p];return n.value=b,b}function u(c=1){return l(a.value+c)}function s(c=1){return u(c)}function i(c=1){return u(-c)}function f(){var c,v;return(v=d.toValue((c=t?.initialValue)!=null?c:d.toValue(e)[0]))!=null?v:void 0}return r.watch(o,()=>l(a.value)),{state:n,index:a,next:s,prev:i,go:l}}function gn(e={}){const{valueDark:t="dark",valueLight:n="",window:o=I}=e,a=He({...e,onChanged:(s,i)=>{var f;e.onChanged?(f=e.onChanged)==null||f.call(e,s==="dark",i,s):i(s)},modes:{dark:t,light:n}}),l=r.computed(()=>a.system?a.system.value:Te({window:o}).value?"dark":"light");return r.computed({get(){return a.value==="dark"},set(s){const i=s?"dark":"light";l.value===i?a.value="auto":a.value=i}})}function je(e){return e}function hn(e,t){return e.value=t}function wn(e){return e?typeof e=="function"?e:ae:je}function bn(e){return e?typeof e=="function"?e:ae:je}function ze(e,t={}){const{clone:n=!1,dump:o=wn(n),parse:a=bn(n),setSource:l=hn}=t;function u(){return r.markRaw({snapshot:o(e.value),timestamp:d.timestamp()})}const s=r.ref(u()),i=r.ref([]),f=r.ref([]),c=E=>{l(e,a(E.snapshot)),s.value=E},v=()=>{i.value.unshift(s.value),s.value=u(),t.capacity&&i.value.length>t.capacity&&i.value.splice(t.capacity,Number.POSITIVE_INFINITY),f.value.length&&f.value.splice(0,f.value.length)},g=()=>{i.value.splice(0,i.value.length),f.value.splice(0,f.value.length)},p=()=>{const E=i.value.shift();E&&(f.value.unshift(s.value),c(E))},b=()=>{const E=f.value.shift();E&&(i.value.unshift(s.value),c(E))},y=()=>{c(s.value)},h=r.computed(()=>[s.value,...i.value]),m=r.computed(()=>i.value.length>0),w=r.computed(()=>f.value.length>0);return{source:e,undoStack:i,redoStack:f,last:s,history:h,canUndo:m,canRedo:w,clear:g,commit:v,reset:y,undo:p,redo:b}}function _e(e,t={}){const{deep:n=!1,flush:o="pre",eventFilter:a}=t,{eventFilter:l,pause:u,resume:s,isActive:i}=d.pausableFilter(a),{ignoreUpdates:f,ignorePrevAsyncUpdates:c,stop:v}=d.watchIgnorable(e,h,{deep:n,flush:o,eventFilter:l});function g(k,_){c(),f(()=>{k.value=_})}const p=ze(e,{...t,clone:t.clone||n,setSource:g}),{clear:b,commit:y}=p;function h(){c(),y()}function m(k){s(),k&&h()}function w(k){let _=!1;const A=()=>_=!0;f(()=>{k(A)}),_||h()}function E(){v(),b()}return{...p,isTracking:i,pause:u,resume:m,commit:h,batch:w,dispose:E}}function Sn(e,t={}){const n=t.debounce?d.debounceFilter(t.debounce):void 0;return{..._e(e,{...t,eventFilter:n})}}function En(e={}){const{window:t=I,eventFilter:n=d.bypassFilter}=e,o=r.ref({x:null,y:null,z:null}),a=r.ref({alpha:null,beta:null,gamma:null}),l=r.ref(0),u=r.ref({x:null,y:null,z:null});if(t){const s=d.createFilterWrapper(n,i=>{o.value=i.acceleration,u.value=i.accelerationIncludingGravity,a.value=i.rotationRate,l.value=i.interval});O(t,"devicemotion",s)}return{acceleration:o,accelerationIncludingGravity:u,rotationRate:a,interval:l}}function qe(e={}){const{window:t=I}=e,n=x(()=>t&&"DeviceOrientationEvent"in t),o=r.ref(!1),a=r.ref(null),l=r.ref(null),u=r.ref(null);return t&&n.value&&O(t,"deviceorientation",s=>{o.value=s.absolute,a.value=s.alpha,l.value=s.beta,u.value=s.gamma}),{isSupported:n,isAbsolute:o,alpha:a,beta:l,gamma:u}}function Tn(e={}){const{window:t=I}=e,n=r.ref(1);if(t){let o=function(){n.value=t.devicePixelRatio,a(),l=t.matchMedia(`(resolution: ${n.value}dppx)`),l.addEventListener("change",o,{once:!0})},a=function(){l?.removeEventListener("change",o)},l;o(),d.tryOnScopeDispose(a)}return{pixelRatio:n}}function kn(e={}){const{navigator:t=H,requestPermissions:n=!1,constraints:o={audio:!0,video:!0},onUpdated:a}=e,l=r.ref([]),u=r.computed(()=>l.value.filter(b=>b.kind==="videoinput")),s=r.computed(()=>l.value.filter(b=>b.kind==="audioinput")),i=r.computed(()=>l.value.filter(b=>b.kind==="audiooutput")),f=x(()=>t&&t.mediaDevices&&t.mediaDevices.enumerateDevices),c=r.ref(!1);let v;async function g(){f.value&&(l.value=await t.mediaDevices.enumerateDevices(),a?.(l.value),v&&(v.getTracks().forEach(b=>b.stop()),v=null))}async function p(){if(!f.value)return!1;if(c.value)return!0;const{state:b,query:y}=de("camera",{controls:!0});if(await y(),b.value!=="granted"){let h=!0;try{v=await t.mediaDevices.getUserMedia(o)}catch{v=null,h=!1}g(),c.value=h}else c.value=!0;return c.value}return f.value&&(n&&p(),O(t.mediaDevices,"devicechange",g),g()),{devices:l,ensurePermissions:p,permissionGranted:c,videoInputs:u,audioInputs:s,audioOutputs:i,isSupported:f}}function On(e={}){var t;const n=r.ref((t=e.enabled)!=null?t:!1),o=e.video,a=e.audio,{navigator:l=H}=e,u=x(()=>{var p;return(p=l?.mediaDevices)==null?void 0:p.getDisplayMedia}),s={audio:a,video:o},i=r.shallowRef();async function f(){var p;if(!(!u.value||i.value))return i.value=await l.mediaDevices.getDisplayMedia(s),(p=i.value)==null||p.getTracks().forEach(b=>b.addEventListener("ended",v)),i.value}async function c(){var p;(p=i.value)==null||p.getTracks().forEach(b=>b.stop()),i.value=void 0}function v(){c(),n.value=!1}async function g(){return await f(),i.value&&(n.value=!0),i.value}return r.watch(n,p=>{p?f():c()},{immediate:!0}),{isSupported:u,stream:i,start:g,stop:v,enabled:n}}function Ge(e={}){const{document:t=B}=e;if(!t)return r.ref("visible");const n=r.ref(t.visibilityState);return O(t,"visibilitychange",()=>{n.value=t.visibilityState}),n}function _n(e,t={}){var n,o;const{pointerTypes:a,preventDefault:l,stopPropagation:u,exact:s,onMove:i,onEnd:f,onStart:c,initialValue:v,axis:g="both",draggingElement:p=I,containerElement:b,handle:y=e,buttons:h=[0]}=t,m=r.ref((n=d.toValue(v))!=null?n:{x:0,y:0}),w=r.ref(),E=F=>a?a.includes(F.pointerType):!0,k=F=>{d.toValue(l)&&F.preventDefault(),d.toValue(u)&&F.stopPropagation()},_=F=>{var C;if(!d.toValue(h).includes(F.button)||d.toValue(t.disabled)||!E(F)||d.toValue(s)&&F.target!==d.toValue(e))return;const P=d.toValue(b),R=(C=P?.getBoundingClientRect)==null?void 0:C.call(P),V=d.toValue(e).getBoundingClientRect(),L={x:F.clientX-(P?V.left-R.left+P.scrollLeft:V.left),y:F.clientY-(P?V.top-R.top+P.scrollTop:V.top)};c?.(L,F)!==!1&&(w.value=L,k(F))},A=F=>{if(d.toValue(t.disabled)||!E(F)||!w.value)return;const C=d.toValue(b),P=d.toValue(e).getBoundingClientRect();let{x:R,y:V}=m.value;(g==="x"||g==="both")&&(R=F.clientX-w.value.x,C&&(R=Math.min(Math.max(0,R),C.scrollWidth-P.width))),(g==="y"||g==="both")&&(V=F.clientY-w.value.y,C&&(V=Math.min(Math.max(0,V),C.scrollHeight-P.height))),m.value={x:R,y:V},i?.(m.value,F),k(F)},T=F=>{d.toValue(t.disabled)||!E(F)||w.value&&(w.value=void 0,f?.(m.value,F),k(F))};if(d.isClient){const F={capture:(o=t.capture)!=null?o:!0};O(y,"pointerdown",_,F),O(p,"pointermove",A,F),O(p,"pointerup",T,F)}return{...d.toRefs(m),position:m,isDragging:r.computed(()=>!!w.value),style:r.computed(()=>`left:${m.value.x}px;top:${m.value.y}px;`)}}function Rn(e,t={}){var n,o;const a=r.ref(!1),l=r.shallowRef(null);let u=0,s=!0;if(d.isClient){const i=typeof t=="function"?{onDrop:t}:t,f=(n=i.multiple)!=null?n:!0,c=(o=i.preventDefaultForUnhandled)!=null?o:!1,v=y=>{var h,m;const w=Array.from((m=(h=y.dataTransfer)==null?void 0:h.files)!=null?m:[]);return w.length===0?null:f?w:[w[0]]},g=y=>{if(i.dataTypes){const h=r.unref(i.dataTypes);return typeof h=="function"?h(y):h?h.some(m=>y.includes(m)):!0}return!0},p=y=>{var h,m;const w=Array.from((m=(h=y.dataTransfer)==null?void 0:h.items)!=null?m:[]),E=w.map(A=>A.type),k=g(E),_=f||w.length<=1;return k&&_},b=(y,h)=>{var m,w,E,k;if(s=p(y),!s){c&&y.preventDefault(),y.dataTransfer&&(y.dataTransfer.dropEffect="none");return}y.preventDefault(),y.dataTransfer&&(y.dataTransfer.dropEffect="copy");const _=v(y);switch(h){case"enter":u+=1,a.value=!0,(m=i.onEnter)==null||m.call(i,null,y);break;case"over":(w=i.onOver)==null||w.call(i,null,y);break;case"leave":u-=1,u===0&&(a.value=!1),(E=i.onLeave)==null||E.call(i,null,y);break;case"drop":u=0,a.value=!1,s&&(l.value=_,(k=i.onDrop)==null||k.call(i,_,y));break}};O(e,"dragenter",y=>b(y,"enter")),O(e,"dragover",y=>b(y,"over")),O(e,"dragleave",y=>b(y,"leave")),O(e,"drop",y=>b(y,"drop"))}return{files:l,isOverDropZone:a}}function ge(e,t,n={}){const{window:o=I,...a}=n;let l;const u=x(()=>o&&"ResizeObserver"in o),s=()=>{l&&(l.disconnect(),l=void 0)},i=r.computed(()=>{const v=d.toValue(e);return Array.isArray(v)?v.map(g=>N(g)):[N(v)]}),f=r.watch(i,v=>{if(s(),u.value&&o){l=new ResizeObserver(t);for(const g of v)g&&l.observe(g,a)}},{immediate:!0,flush:"post"}),c=()=>{s(),f()};return d.tryOnScopeDispose(c),{isSupported:u,stop:c}}function Fn(e,t={}){const{reset:n=!0,windowResize:o=!0,windowScroll:a=!0,immediate:l=!0,updateTiming:u="sync"}=t,s=r.ref(0),i=r.ref(0),f=r.ref(0),c=r.ref(0),v=r.ref(0),g=r.ref(0),p=r.ref(0),b=r.ref(0);function y(){const m=N(e);if(!m){n&&(s.value=0,i.value=0,f.value=0,c.value=0,v.value=0,g.value=0,p.value=0,b.value=0);return}const w=m.getBoundingClientRect();s.value=w.height,i.value=w.bottom,f.value=w.left,c.value=w.right,v.value=w.top,g.value=w.width,p.value=w.x,b.value=w.y}function h(){u==="sync"?y():u==="next-frame"&&requestAnimationFrame(()=>y())}return ge(e,h),r.watch(()=>N(e),m=>!m&&h()),oe(e,h,{attributeFilter:["style","class"]}),a&&O("scroll",h,{capture:!0,passive:!0}),o&&O("resize",h,{passive:!0}),d.tryOnMounted(()=>{l&&h()}),{height:s,bottom:i,left:f,right:c,top:v,width:g,x:p,y:b,update:h}}function Pn(e){const{x:t,y:n,document:o=B,multiple:a,interval:l="requestAnimationFrame",immediate:u=!0}=e,s=x(()=>d.toValue(a)?o&&"elementsFromPoint"in o:o&&"elementFromPoint"in o),i=r.ref(null),f=()=>{var v,g;i.value=d.toValue(a)?(v=o?.elementsFromPoint(d.toValue(t),d.toValue(n)))!=null?v:[]:(g=o?.elementFromPoint(d.toValue(t),d.toValue(n)))!=null?g:null},c=l==="requestAnimationFrame"?ee(f,{immediate:u}):d.useIntervalFn(f,l,{immediate:u});return{isSupported:s,element:i,...c}}function Cn(e,t={}){const{delayEnter:n=0,delayLeave:o=0,window:a=I}=t,l=r.ref(!1);let u;const s=i=>{const f=i?n:o;u&&(clearTimeout(u),u=void 0),f?u=setTimeout(()=>l.value=i,f):l.value=i};return a&&(O(e,"mouseenter",()=>s(!0),{passive:!0}),O(e,"mouseleave",()=>s(!1),{passive:!0})),l}function Ye(e,t={width:0,height:0},n={}){const{window:o=I,box:a="content-box"}=n,l=r.computed(()=>{var v,g;return(g=(v=N(e))==null?void 0:v.namespaceURI)==null?void 0:g.includes("svg")}),u=r.ref(t.width),s=r.ref(t.height),{stop:i}=ge(e,([v])=>{const g=a==="border-box"?v.borderBoxSize:a==="content-box"?v.contentBoxSize:v.devicePixelContentBoxSize;if(o&&l.value){const p=N(e);if(p){const b=p.getBoundingClientRect();u.value=b.width,s.value=b.height}}else if(g){const p=Array.isArray(g)?g:[g];u.value=p.reduce((b,{inlineSize:y})=>b+y,0),s.value=p.reduce((b,{blockSize:y})=>b+y,0)}else u.value=v.contentRect.width,s.value=v.contentRect.height},n);d.tryOnMounted(()=>{const v=N(e);v&&(u.value="offsetWidth"in v?v.offsetWidth:t.width,s.value="offsetHeight"in v?v.offsetHeight:t.height)});const f=r.watch(()=>N(e),v=>{u.value=v?t.width:0,s.value=v?t.height:0});function c(){i(),f()}return{width:u,height:s,stop:c}}function Xe(e,t,n={}){const{root:o,rootMargin:a="0px",threshold:l=0,window:u=I,immediate:s=!0}=n,i=x(()=>u&&"IntersectionObserver"in u),f=r.computed(()=>{const b=d.toValue(e);return(Array.isArray(b)?b:[b]).map(N).filter(d.notNullish)});let c=d.noop;const v=r.ref(s),g=i.value?r.watch(()=>[f.value,N(o),v.value],([b,y])=>{if(c(),!v.value||!b.length)return;const h=new IntersectionObserver(t,{root:N(y),rootMargin:a,threshold:l});b.forEach(m=>m&&h.observe(m)),c=()=>{h.disconnect(),c=d.noop}},{immediate:s,flush:"post"}):d.noop,p=()=>{c(),g(),v.value=!1};return d.tryOnScopeDispose(p),{isSupported:i,isActive:v,pause(){c(),v.value=!1},resume(){v.value=!0},stop:p}}function Ke(e,t={}){const{window:n=I,scrollTarget:o,threshold:a=0}=t,l=r.ref(!1);return Xe(e,u=>{let s=l.value,i=0;for(const f of u)f.time>=i&&(i=f.time,s=f.isIntersecting);l.value=s},{root:o,window:n,threshold:a}),l}const se=new Map;function Vn(e){const t=r.getCurrentScope();function n(s){var i;const f=se.get(e)||new Set;f.add(s),se.set(e,f);const c=()=>a(s);return(i=t?.cleanups)==null||i.push(c),c}function o(s){function i(...f){a(i),s(...f)}return n(i)}function a(s){const i=se.get(e);i&&(i.delete(s),i.size||l())}function l(){se.delete(e)}function u(s,i){var f;(f=se.get(e))==null||f.forEach(c=>c(s,i))}return{on:n,once:o,off:a,emit:u,reset:l}}function An(e){return e===!0?{}:e}function In(e,t=[],n={}){const o=r.ref(null),a=r.ref(null),l=r.ref("CONNECTING"),u=r.ref(null),s=r.shallowRef(null),i=d.toRef(e),f=r.shallowRef(null);let c=!1,v=0;const{withCredentials:g=!1,immediate:p=!0}=n,b=()=>{d.isClient&&u.value&&(u.value.close(),u.value=null,l.value="CLOSED",c=!0)},y=()=>{if(c||typeof i.value>"u")return;const m=new EventSource(i.value,{withCredentials:g});l.value="CONNECTING",u.value=m,m.onopen=()=>{l.value="OPEN",s.value=null},m.onerror=w=>{if(l.value="CLOSED",s.value=w,m.readyState===2&&!c&&n.autoReconnect){m.close();const{retries:E=-1,delay:k=1e3,onFailed:_}=An(n.autoReconnect);v+=1,typeof E=="number"&&(E<0||v<E)||typeof E=="function"&&E()?setTimeout(y,k):_?.()}},m.onmessage=w=>{o.value=null,a.value=w.data,f.value=w.lastEventId};for(const w of t)O(m,w,E=>{o.value=w,a.value=E.data||null})},h=()=>{d.isClient&&(b(),c=!1,v=0,y())};return p&&r.watch(i,h,{immediate:!0}),d.tryOnScopeDispose(b),{eventSource:u,event:o,data:a,status:l,error:s,open:h,close:b,lastEventId:f}}function Mn(e={}){const{initialValue:t=""}=e,n=x(()=>typeof window<"u"&&"EyeDropper"in window),o=r.ref(t);async function a(l){if(!n.value)return;const s=await new window.EyeDropper().open(l);return o.value=s.sRGBHex,s}return{isSupported:n,sRGBHex:o,open:a}}function Ln(e=null,t={}){const{baseUrl:n="",rel:o="icon",document:a=B}=t,l=d.toRef(e),u=s=>{const i=a?.head.querySelectorAll(`link[rel*="${o}"]`);if(!i||i.length===0){const f=a?.createElement("link");f&&(f.rel=o,f.href=`${n}${s}`,f.type=`image/${s.split(".").pop()}`,a?.head.append(f));return}i?.forEach(f=>f.href=`${n}${s}`)};return r.watch(l,(s,i)=>{typeof s=="string"&&s!==i&&u(s)},{immediate:!0}),l}const Nn={json:"application/json",text:"text/plain"};function he(e){return e&&d.containsProp(e,"immediate","refetch","initialData","timeout","beforeFetch","afterFetch","onFetchError","fetch","updateDataOnError")}const xn=/^(?:[a-z][a-z\d+\-.]*:)?\/\//i;function $n(e){return xn.test(e)}function ie(e){return typeof Headers<"u"&&e instanceof Headers?Object.fromEntries(e.entries()):e}function le(e,...t){return e==="overwrite"?async n=>{const o=t[t.length-1];return o?{...n,...await o(n)}:n}:async n=>{for(const o of t)o&&(n={...n,...await o(n)});return n}}function Wn(e={}){const t=e.combination||"chain",n=e.options||{},o=e.fetchOptions||{};function a(l,...u){const s=r.computed(()=>{const c=d.toValue(e.baseUrl),v=d.toValue(l);return c&&!$n(v)?Un(c,v):v});let i=n,f=o;return u.length>0&&(he(u[0])?i={...i,...u[0],beforeFetch:le(t,n.beforeFetch,u[0].beforeFetch),afterFetch:le(t,n.afterFetch,u[0].afterFetch),onFetchError:le(t,n.onFetchError,u[0].onFetchError)}:f={...f,...u[0],headers:{...ie(f.headers)||{},...ie(u[0].headers)||{}}}),u.length>1&&he(u[1])&&(i={...i,...u[1],beforeFetch:le(t,n.beforeFetch,u[1].beforeFetch),afterFetch:le(t,n.afterFetch,u[1].afterFetch),onFetchError:le(t,n.onFetchError,u[1].onFetchError)}),Je(s,f,i)}return a}function Je(e,...t){var n;const o=typeof AbortController=="function";let a={},l={immediate:!0,refetch:!1,timeout:0,updateDataOnError:!1};const u={method:"GET",type:"text",payload:void 0};t.length>0&&(he(t[0])?l={...l,...t[0]}:a=t[0]),t.length>1&&he(t[1])&&(l={...l,...t[1]});const{fetch:s=(n=I)==null?void 0:n.fetch,initialData:i,timeout:f}=l,c=d.createEventHook(),v=d.createEventHook(),g=d.createEventHook(),p=r.ref(!1),b=r.ref(!1),y=r.ref(!1),h=r.ref(null),m=r.shallowRef(null),w=r.shallowRef(null),E=r.shallowRef(i||null),k=r.computed(()=>o&&b.value);let _,A;const T=()=>{o&&(_?.abort(),_=new AbortController,_.signal.onabort=()=>y.value=!0,a={...a,signal:_.signal})},F=M=>{b.value=M,p.value=!M};f&&(A=d.useTimeoutFn(T,f,{immediate:!1}));let C=0;const P=async(M=!1)=>{var W,Z;T(),F(!0),w.value=null,h.value=null,y.value=!1,C+=1;const G=C,te={method:u.method,headers:{}};if(u.payload){const j=ie(te.headers),D=d.toValue(u.payload),Rt=Object.getPrototypeOf(D);!u.payloadType&&D&&(Rt===Object.prototype||Array.isArray(Rt))&&!(D instanceof FormData)&&(u.payloadType="json"),u.payloadType&&(j["Content-Type"]=(W=Nn[u.payloadType])!=null?W:u.payloadType),te.body=u.payloadType==="json"?JSON.stringify(D):D}let _t=!1;const ce={url:d.toValue(e),options:{...te,...a},cancel:()=>{_t=!0}};if(l.beforeFetch&&Object.assign(ce,await l.beforeFetch(ce)),_t||!s)return F(!1),Promise.resolve(null);let ne=null;return A&&A.start(),s(ce.url,{...te,...ce.options,headers:{...ie(te.headers),...ie((Z=ce.options)==null?void 0:Z.headers)}}).then(async j=>{if(m.value=j,h.value=j.status,ne=await j.clone()[u.type](),!j.ok)throw E.value=i||null,new Error(j.statusText);return l.afterFetch&&({data:ne}=await l.afterFetch({data:ne,response:j})),E.value=ne,c.trigger(j),j}).catch(async j=>{let D=j.message||j.name;if(l.onFetchError&&({error:D,data:ne}=await l.onFetchError({data:ne,error:j,response:m.value})),w.value=D,l.updateDataOnError&&(E.value=ne),v.trigger(j),M)throw j;return null}).finally(()=>{G===C&&F(!1),A&&A.stop(),g.trigger(null)})},R=d.toRef(l.refetch);r.watch([R,d.toRef(e)],([M])=>M&&P(),{deep:!0});const V={isFinished:r.readonly(p),isFetching:r.readonly(b),statusCode:h,response:m,error:w,data:E,canAbort:k,aborted:y,abort:T,execute:P,onFetchResponse:c.on,onFetchError:v.on,onFetchFinally:g.on,get:L("GET"),put:L("PUT"),post:L("POST"),delete:L("DELETE"),patch:L("PATCH"),head:L("HEAD"),options:L("OPTIONS"),json:U("json"),text:U("text"),blob:U("blob"),arrayBuffer:U("arrayBuffer"),formData:U("formData")};function L(M){return(W,Z)=>{if(!b.value)return u.method=M,u.payload=W,u.payloadType=Z,r.isRef(u.payload)&&r.watch([R,d.toRef(u.payload)],([G])=>G&&P(),{deep:!0}),{...V,then(G,te){return $().then(G,te)}}}}function $(){return new Promise((M,W)=>{d.until(p).toBe(!0).then(()=>M(V)).catch(W)})}function U(M){return()=>{if(!b.value)return u.type=M,{...V,then(W,Z){return $().then(W,Z)}}}}return l.immediate&&Promise.resolve().then(()=>P()),{...V,then(M,W){return $().then(M,W)}}}function Un(e,t){return!e.endsWith("/")&&!t.startsWith("/")?`${e}/${t}`:e.endsWith("/")&&t.startsWith("/")?`${e.slice(0,-1)}${t}`:`${e}${t}`}const Hn={multiple:!0,accept:"*",reset:!1,directory:!1};function Bn(e={}){const{document:t=B}=e,n=r.ref(null),{on:o,trigger:a}=d.createEventHook(),{on:l,trigger:u}=d.createEventHook();let s;t&&(s=t.createElement("input"),s.type="file",s.onchange=c=>{const v=c.target;n.value=v.files,a(n.value)},s.oncancel=()=>{u()});const i=()=>{n.value=null,s&&s.value&&(s.value="",a(null))},f=c=>{if(!s)return;const v={...Hn,...e,...c};s.multiple=v.multiple,s.accept=v.accept,s.webkitdirectory=v.directory,d.hasOwn(v,"capture")&&(s.capture=v.capture),v.reset&&i(),s.click()};return{files:r.readonly(n),open:f,reset:i,onCancel:l,onChange:o}}function jn(e={}){const{window:t=I,dataType:n="Text"}=e,o=t,a=x(()=>o&&"showSaveFilePicker"in o&&"showOpenFilePicker"in o),l=r.ref(),u=r.ref(),s=r.ref(),i=r.computed(()=>{var w,E;return(E=(w=s.value)==null?void 0:w.name)!=null?E:""}),f=r.computed(()=>{var w,E;return(E=(w=s.value)==null?void 0:w.type)!=null?E:""}),c=r.computed(()=>{var w,E;return(E=(w=s.value)==null?void 0:w.size)!=null?E:0}),v=r.computed(()=>{var w,E;return(E=(w=s.value)==null?void 0:w.lastModified)!=null?E:0});async function g(w={}){if(!a.value)return;const[E]=await o.showOpenFilePicker({...d.toValue(e),...w});l.value=E,await m()}async function p(w={}){a.value&&(l.value=await o.showSaveFilePicker({...e,...w}),u.value=void 0,await m())}async function b(w={}){if(a.value){if(!l.value)return y(w);if(u.value){const E=await l.value.createWritable();await E.write(u.value),await E.close()}await h()}}async function y(w={}){if(a.value){if(l.value=await o.showSaveFilePicker({...e,...w}),u.value){const E=await l.value.createWritable();await E.write(u.value),await E.close()}await h()}}async function h(){var w;s.value=await((w=l.value)==null?void 0:w.getFile())}async function m(){var w,E;await h();const k=d.toValue(n);k==="Text"?u.value=await((w=s.value)==null?void 0:w.text()):k==="ArrayBuffer"?u.value=await((E=s.value)==null?void 0:E.arrayBuffer()):k==="Blob"&&(u.value=s.value)}return r.watch(()=>d.toValue(n),m),{isSupported:a,data:u,file:s,fileName:i,fileMIME:f,fileSize:c,fileLastModified:v,open:g,create:p,save:b,saveAs:y,updateData:m}}function zn(e,t={}){const{initialValue:n=!1,focusVisible:o=!1,preventScroll:a=!1}=t,l=r.ref(!1),u=r.computed(()=>N(e));O(u,"focus",i=>{var f,c;(!o||(c=(f=i.target).matches)!=null&&c.call(f,":focus-visible"))&&(l.value=!0)}),O(u,"blur",()=>l.value=!1);const s=r.computed({get:()=>l.value,set(i){var f,c;!i&&l.value?(f=u.value)==null||f.blur():i&&!l.value&&((c=u.value)==null||c.focus({preventScroll:a}))}});return r.watch(u,()=>{s.value=n},{immediate:!0,flush:"post"}),{focused:s}}const qn="focusin",Gn="focusout";function Yn(e,t={}){const{window:n=I}=t,o=r.computed(()=>N(e)),a=r.ref(!1),l=r.computed(()=>a.value),u=Le(t);return!n||!u.value?{focused:l}:(O(o,qn,()=>a.value=!0),O(o,Gn,()=>a.value=!1),{focused:l})}function Xn(e){var t;const n=r.ref(0);if(typeof performance>"u")return n;const o=(t=e?.every)!=null?t:10;let a=performance.now(),l=0;return ee(()=>{if(l+=1,l>=o){const u=performance.now(),s=u-a;n.value=Math.round(1e3/(s/l)),a=u,l=0}}),n}const Qe=["fullscreenchange","webkitfullscreenchange","webkitendfullscreen","mozfullscreenchange","MSFullscreenChange"];function Kn(e,t={}){const{document:n=B,autoExit:o=!1}=t,a=r.computed(()=>{var m;return(m=N(e))!=null?m:n?.querySelector("html")}),l=r.ref(!1),u=r.computed(()=>["requestFullscreen","webkitRequestFullscreen","webkitEnterFullscreen","webkitEnterFullScreen","webkitRequestFullScreen","mozRequestFullScreen","msRequestFullscreen"].find(m=>n&&m in n||a.value&&m in a.value)),s=r.computed(()=>["exitFullscreen","webkitExitFullscreen","webkitExitFullScreen","webkitCancelFullScreen","mozCancelFullScreen","msExitFullscreen"].find(m=>n&&m in n||a.value&&m in a.value)),i=r.computed(()=>["fullScreen","webkitIsFullScreen","webkitDisplayingFullscreen","mozFullScreen","msFullscreenElement"].find(m=>n&&m in n||a.value&&m in a.value)),f=["fullscreenElement","webkitFullscreenElement","mozFullScreenElement","msFullscreenElement"].find(m=>n&&m in n),c=x(()=>a.value&&n&&u.value!==void 0&&s.value!==void 0&&i.value!==void 0),v=()=>f?n?.[f]===a.value:!1,g=()=>{if(i.value){if(n&&n[i.value]!=null)return n[i.value];{const m=a.value;if(m?.[i.value]!=null)return!!m[i.value]}}return!1};async function p(){if(!(!c.value||!l.value)){if(s.value)if(n?.[s.value]!=null)await n[s.value]();else{const m=a.value;m?.[s.value]!=null&&await m[s.value]()}l.value=!1}}async function b(){if(!c.value||l.value)return;g()&&await p();const m=a.value;u.value&&m?.[u.value]!=null&&(await m[u.value](),l.value=!0)}async function y(){await(l.value?p():b())}const h=()=>{const m=g();(!m||m&&v())&&(l.value=m)};return O(n,Qe,h,!1),O(()=>N(a),Qe,h,!1),o&&d.tryOnScopeDispose(p),{isSupported:c,isFullscreen:l,enter:b,exit:p,toggle:y}}function Jn(e){return r.computed(()=>e.value?{buttons:{a:e.value.buttons[0],b:e.value.buttons[1],x:e.value.buttons[2],y:e.value.buttons[3]},bumper:{left:e.value.buttons[4],right:e.value.buttons[5]},triggers:{left:e.value.buttons[6],right:e.value.buttons[7]},stick:{left:{horizontal:e.value.axes[0],vertical:e.value.axes[1],button:e.value.buttons[10]},right:{horizontal:e.value.axes[2],vertical:e.value.axes[3],button:e.value.buttons[11]}},dpad:{up:e.value.buttons[12],down:e.value.buttons[13],left:e.value.buttons[14],right:e.value.buttons[15]},back:e.value.buttons[8],start:e.value.buttons[9]}:null)}function Qn(e={}){const{navigator:t=H}=e,n=x(()=>t&&"getGamepads"in t),o=r.ref([]),a=d.createEventHook(),l=d.createEventHook(),u=p=>{const b=[],y="vibrationActuator"in p?p.vibrationActuator:null;return y&&b.push(y),p.hapticActuators&&b.push(...p.hapticActuators),{id:p.id,index:p.index,connected:p.connected,mapping:p.mapping,timestamp:p.timestamp,vibrationActuator:p.vibrationActuator,hapticActuators:b,axes:p.axes.map(h=>h),buttons:p.buttons.map(h=>({pressed:h.pressed,touched:h.touched,value:h.value}))}},s=()=>{const p=t?.getGamepads()||[];for(const b of p)b&&o.value[b.index]&&(o.value[b.index]=u(b))},{isActive:i,pause:f,resume:c}=ee(s),v=p=>{o.value.some(({index:b})=>b===p.index)||(o.value.push(u(p)),a.trigger(p.index)),c()},g=p=>{o.value=o.value.filter(b=>b.index!==p.index),l.trigger(p.index)};return O("gamepadconnected",p=>v(p.gamepad)),O("gamepaddisconnected",p=>g(p.gamepad)),d.tryOnMounted(()=>{const p=t?.getGamepads()||[];for(const b of p)b&&o.value[b.index]&&v(b)}),f(),{isSupported:n,onConnected:a.on,onDisconnected:l.on,gamepads:o,pause:f,resume:c,isActive:i}}function Zn(e={}){const{enableHighAccuracy:t=!0,maximumAge:n=3e4,timeout:o=27e3,navigator:a=H,immediate:l=!0}=e,u=x(()=>a&&"geolocation"in a),s=r.ref(null),i=r.shallowRef(null),f=r.ref({accuracy:0,latitude:Number.POSITIVE_INFINITY,longitude:Number.POSITIVE_INFINITY,altitude:null,altitudeAccuracy:null,heading:null,speed:null});function c(b){s.value=b.timestamp,f.value=b.coords,i.value=null}let v;function g(){u.value&&(v=a.geolocation.watchPosition(c,b=>i.value=b,{enableHighAccuracy:t,maximumAge:n,timeout:o}))}l&&g();function p(){v&&a&&a.geolocation.clearWatch(v)}return d.tryOnScopeDispose(()=>{p()}),{isSupported:u,coords:f,locatedAt:s,error:i,resume:g,pause:p}}const Dn=["mousemove","mousedown","resize","keydown","touchstart","wheel"],eo=6e4;function to(e=eo,t={}){const{initialState:n=!1,listenForVisibilityChange:o=!0,events:a=Dn,window:l=I,eventFilter:u=d.throttleFilter(50)}=t,s=r.ref(n),i=r.ref(d.timestamp());let f;const c=()=>{s.value=!1,clearTimeout(f),f=setTimeout(()=>s.value=!0,e)},v=d.createFilterWrapper(u,()=>{i.value=d.timestamp(),c()});if(l){const g=l.document;for(const p of a)O(l,p,v,{passive:!0});o&&O(g,"visibilitychange",()=>{g.hidden||v()}),c()}return{idle:s,lastActive:i,reset:c}}async function no(e){return new Promise((t,n)=>{const o=new Image,{src:a,srcset:l,sizes:u,class:s,loading:i,crossorigin:f,referrerPolicy:c}=e;o.src=a,l&&(o.srcset=l),u&&(o.sizes=u),s&&(o.className=s),i&&(o.loading=i),f&&(o.crossOrigin=f),c&&(o.referrerPolicy=c),o.onload=()=>t(o),o.onerror=n})}function oo(e,t={}){const n=Ne(()=>no(d.toValue(e)),void 0,{resetOnExecute:!0,...t});return r.watch(()=>d.toValue(e),()=>n.execute(t.delay),{deep:!0}),n}function we(e){return typeof Window<"u"&&e instanceof Window?e.document.documentElement:typeof Document<"u"&&e instanceof Document?e.documentElement:e}const Ze=1;function De(e,t={}){const{throttle:n=0,idle:o=200,onStop:a=d.noop,onScroll:l=d.noop,offset:u={left:0,right:0,top:0,bottom:0},eventListenerOptions:s={capture:!1,passive:!0},behavior:i="auto",window:f=I,onError:c=T=>{console.error(T)}}=t,v=r.ref(0),g=r.ref(0),p=r.computed({get(){return v.value},set(T){y(T,void 0)}}),b=r.computed({get(){return g.value},set(T){y(void 0,T)}});function y(T,F){var C,P,R,V;if(!f)return;const L=d.toValue(e);if(!L)return;(R=L instanceof Document?f.document.body:L)==null||R.scrollTo({top:(C=d.toValue(F))!=null?C:b.value,left:(P=d.toValue(T))!=null?P:p.value,behavior:d.toValue(i)});const $=((V=L?.document)==null?void 0:V.documentElement)||L?.documentElement||L;p!=null&&(v.value=$.scrollLeft),b!=null&&(g.value=$.scrollTop)}const h=r.ref(!1),m=r.reactive({left:!0,right:!1,top:!0,bottom:!1}),w=r.reactive({left:!1,right:!1,top:!1,bottom:!1}),E=T=>{h.value&&(h.value=!1,w.left=!1,w.right=!1,w.top=!1,w.bottom=!1,a(T))},k=d.useDebounceFn(E,n+o),_=T=>{var F;if(!f)return;const C=((F=T?.document)==null?void 0:F.documentElement)||T?.documentElement||N(T),{display:P,flexDirection:R}=getComputedStyle(C),V=C.scrollLeft;w.left=V<v.value,w.right=V>v.value;const L=Math.abs(V)<=(u.left||0),$=Math.abs(V)+C.clientWidth>=C.scrollWidth-(u.right||0)-Ze;P==="flex"&&R==="row-reverse"?(m.left=$,m.right=L):(m.left=L,m.right=$),v.value=V;let U=C.scrollTop;T===f.document&&!U&&(U=f.document.body.scrollTop),w.top=U<g.value,w.bottom=U>g.value;const M=Math.abs(U)<=(u.top||0),W=Math.abs(U)+C.clientHeight>=C.scrollHeight-(u.bottom||0)-Ze;P==="flex"&&R==="column-reverse"?(m.top=W,m.bottom=M):(m.top=M,m.bottom=W),g.value=U},A=T=>{var F;if(!f)return;const C=(F=T.target.documentElement)!=null?F:T.target;_(C),h.value=!0,k(T),l(T)};return O(e,"scroll",n?d.useThrottleFn(A,n,!0,!1):A,s),d.tryOnMounted(()=>{try{const T=d.toValue(e);if(!T)return;_(T)}catch(T){c(T)}}),O(e,"scrollend",E,s),{x:p,y:b,isScrolling:h,arrivedState:m,directions:w,measure(){const T=d.toValue(e);f&&T&&_(T)}}}function lo(e,t,n={}){var o;const{direction:a="bottom",interval:l=100,canLoadMore:u=()=>!0}=n,s=r.reactive(De(e,{...n,offset:{[a]:(o=n.distance)!=null?o:0,...n.offset}})),i=r.ref(),f=r.computed(()=>!!i.value),c=r.computed(()=>we(d.toValue(e))),v=Ke(c);function g(){if(s.measure(),!c.value||!v.value||!u(c.value))return;const{scrollHeight:b,clientHeight:y,scrollWidth:h,clientWidth:m}=c.value,w=a==="bottom"||a==="top"?b<=y:h<=m;(s.arrivedState[a]||w)&&(i.value||(i.value=Promise.all([t(s),new Promise(E=>setTimeout(E,l))]).finally(()=>{i.value=null,r.nextTick(()=>g())})))}const p=r.watch(()=>[s.arrivedState[a],v.value],g,{immediate:!0});return d.tryOnUnmounted(p),{isLoading:f,reset(){r.nextTick(()=>g())}}}const ro=["mousedown","mouseup","keydown","keyup"];function ao(e,t={}){const{events:n=ro,document:o=B,initial:a=null}=t,l=r.ref(a);return o&&n.forEach(u=>{O(o,u,s=>{typeof s.getModifierState=="function"&&(l.value=s.getModifierState(e))})}),l}function uo(e,t,n={}){const{window:o=I}=n;return me(e,t,o?.localStorage,n)}const et={ctrl:"control",command:"meta",cmd:"meta",option:"alt",up:"arrowup",down:"arrowdown",left:"arrowleft",right:"arrowright"};function so(e={}){const{reactive:t=!1,target:n=I,aliasMap:o=et,passive:a=!0,onEventFired:l=d.noop}=e,u=r.reactive(new Set),s={toJSON(){return{}},current:u},i=t?r.reactive(s):s,f=new Set,c=new Set;function v(y,h){y in i&&(t?i[y]=h:i[y].value=h)}function g(){u.clear();for(const y of c)v(y,!1)}function p(y,h){var m,w;const E=(m=y.key)==null?void 0:m.toLowerCase(),_=[(w=y.code)==null?void 0:w.toLowerCase(),E].filter(Boolean);E&&(h?u.add(E):u.delete(E));for(const A of _)c.add(A),v(A,h);E==="meta"&&!h?(f.forEach(A=>{u.delete(A),v(A,!1)}),f.clear()):typeof y.getModifierState=="function"&&y.getModifierState("Meta")&&h&&[...u,..._].forEach(A=>f.add(A))}O(n,"keydown",y=>(p(y,!0),l(y)),{passive:a}),O(n,"keyup",y=>(p(y,!1),l(y)),{passive:a}),O("blur",g,{passive:!0}),O("focus",g,{passive:!0});const b=new Proxy(i,{get(y,h,m){if(typeof h!="string")return Reflect.get(y,h,m);if(h=h.toLowerCase(),h in o&&(h=o[h]),!(h in i))if(/[+_-]/.test(h)){const E=h.split(/[+_-]/g).map(k=>k.trim());i[h]=r.computed(()=>E.every(k=>d.toValue(b[k])))}else i[h]=r.ref(!1);const w=Reflect.get(y,h,m);return t?d.toValue(w):w}});return b}function Re(e,t){d.toValue(e)&&t(d.toValue(e))}function io(e){let t=[];for(let n=0;n<e.length;++n)t=[...t,[e.start(n),e.end(n)]];return t}function Fe(e){return Array.from(e).map(({label:t,kind:n,language:o,mode:a,activeCues:l,cues:u,inBandMetadataTrackDispatchType:s},i)=>({id:i,label:t,kind:n,language:o,mode:a,activeCues:l,cues:u,inBandMetadataTrackDispatchType:s}))}const co={src:"",tracks:[]};function fo(e,t={}){e=d.toRef(e),t={...co,...t};const{document:n=B}=t,o=r.ref(0),a=r.ref(0),l=r.ref(!1),u=r.ref(1),s=r.ref(!1),i=r.ref(!1),f=r.ref(!1),c=r.ref(1),v=r.ref(!1),g=r.ref([]),p=r.ref([]),b=r.ref(-1),y=r.ref(!1),h=r.ref(!1),m=n&&"pictureInPictureEnabled"in n,w=d.createEventHook(),E=d.createEventHook(),k=R=>{Re(e,V=>{if(R){const L=typeof R=="number"?R:R.id;V.textTracks[L].mode="disabled"}else for(let L=0;L<V.textTracks.length;++L)V.textTracks[L].mode="disabled";b.value=-1})},_=(R,V=!0)=>{Re(e,L=>{const $=typeof R=="number"?R:R.id;V&&k(),L.textTracks[$].mode="showing",b.value=$})},A=()=>new Promise((R,V)=>{Re(e,async L=>{m&&(y.value?n.exitPictureInPicture().then(R).catch(V):L.requestPictureInPicture().then(R).catch(V))})});r.watchEffect(()=>{if(!n)return;const R=d.toValue(e);if(!R)return;const V=d.toValue(t.src);let L=[];V&&(typeof V=="string"?L=[{src:V}]:Array.isArray(V)?L=V:d.isObject(V)&&(L=[V]),R.querySelectorAll("source").forEach($=>{$.removeEventListener("error",w.trigger),$.remove()}),L.forEach(({src:$,type:U})=>{const M=n.createElement("source");M.setAttribute("src",$),M.setAttribute("type",U||""),M.addEventListener("error",w.trigger),R.appendChild(M)}),R.load())}),d.tryOnScopeDispose(()=>{const R=d.toValue(e);R&&R.querySelectorAll("source").forEach(V=>V.removeEventListener("error",w.trigger))}),r.watch([e,u],()=>{const R=d.toValue(e);R&&(R.volume=u.value)}),r.watch([e,h],()=>{const R=d.toValue(e);R&&(R.muted=h.value)}),r.watch([e,c],()=>{const R=d.toValue(e);R&&(R.playbackRate=c.value)}),r.watchEffect(()=>{if(!n)return;const R=d.toValue(t.tracks),V=d.toValue(e);!R||!R.length||!V||(V.querySelectorAll("track").forEach(L=>L.remove()),R.forEach(({default:L,kind:$,label:U,src:M,srcLang:W},Z)=>{const G=n.createElement("track");G.default=L||!1,G.kind=$,G.label=U,G.src=M,G.srclang=W,G.default&&(b.value=Z),V.appendChild(G)}))});const{ignoreUpdates:T}=d.watchIgnorable(o,R=>{const V=d.toValue(e);V&&(V.currentTime=R)}),{ignoreUpdates:F}=d.watchIgnorable(f,R=>{const V=d.toValue(e);V&&(R?V.play().catch(L=>{throw E.trigger(L),L}):V.pause())});O(e,"timeupdate",()=>T(()=>o.value=d.toValue(e).currentTime)),O(e,"durationchange",()=>a.value=d.toValue(e).duration),O(e,"progress",()=>g.value=io(d.toValue(e).buffered)),O(e,"seeking",()=>l.value=!0),O(e,"seeked",()=>l.value=!1),O(e,["waiting","loadstart"],()=>{s.value=!0,F(()=>f.value=!1)}),O(e,"loadeddata",()=>s.value=!1),O(e,"playing",()=>{s.value=!1,i.value=!1,F(()=>f.value=!0)}),O(e,"ratechange",()=>c.value=d.toValue(e).playbackRate),O(e,"stalled",()=>v.value=!0),O(e,"ended",()=>i.value=!0),O(e,"pause",()=>F(()=>f.value=!1)),O(e,"play",()=>F(()=>f.value=!0)),O(e,"enterpictureinpicture",()=>y.value=!0),O(e,"leavepictureinpicture",()=>y.value=!1),O(e,"volumechange",()=>{const R=d.toValue(e);R&&(u.value=R.volume,h.value=R.muted)});const C=[],P=r.watch([e],()=>{const R=d.toValue(e);R&&(P(),C[0]=O(R.textTracks,"addtrack",()=>p.value=Fe(R.textTracks)),C[1]=O(R.textTracks,"removetrack",()=>p.value=Fe(R.textTracks)),C[2]=O(R.textTracks,"change",()=>p.value=Fe(R.textTracks)))});return d.tryOnScopeDispose(()=>C.forEach(R=>R())),{currentTime:o,duration:a,waiting:s,seeking:l,ended:i,stalled:v,buffered:g,playing:f,rate:c,volume:u,muted:h,tracks:p,selectedTrack:b,enableTrack:_,disableTrack:k,supportsPictureInPicture:m,togglePictureInPicture:A,isPictureInPicture:y,onSourceError:w.on,onPlaybackError:E.on}}function vo(){const e=r.shallowReactive({});return{get:t=>e[t],set:(t,n)=>r.set(e,t,n),has:t=>d.hasOwn(e,t),delete:t=>r.del(e,t),clear:()=>{Object.keys(e).forEach(t=>{r.del(e,t)})}}}function po(e,t){const o=t?.cache?r.shallowReactive(t.cache):r.isVue2?vo():r.shallowReactive(new Map),a=(...c)=>t?.getKey?t.getKey(...c):JSON.stringify(c),l=(c,...v)=>(o.set(c,e(...v)),o.get(c)),u=(...c)=>l(a(...c),...c),s=(...c)=>{o.delete(a(...c))},i=()=>{o.clear()},f=(...c)=>{const v=a(...c);return o.has(v)?o.get(v):l(v,...c)};return f.load=u,f.delete=s,f.clear=i,f.generateKey=a,f.cache=o,f}function yo(e={}){const t=r.ref(),n=x(()=>typeof performance<"u"&&"memory"in performance);if(n.value){const{interval:o=1e3}=e;d.useIntervalFn(()=>{t.value=performance.memory},o,{immediate:e.immediate,immediateCallback:e.immediateCallback})}return{isSupported:n,memory:t}}const mo={page:e=>[e.pageX,e.pageY],client:e=>[e.clientX,e.clientY],screen:e=>[e.screenX,e.screenY],movement:e=>e instanceof Touch?null:[e.movementX,e.movementY]};function tt(e={}){const{type:t="page",touch:n=!0,resetOnTouchEnds:o=!1,initialValue:a={x:0,y:0},window:l=I,target:u=l,scroll:s=!0,eventFilter:i}=e;let f=null,c=0,v=0;const g=r.ref(a.x),p=r.ref(a.y),b=r.ref(null),y=typeof t=="function"?t:mo[t],h=T=>{const F=y(T);f=T,F&&([g.value,p.value]=F,b.value="mouse"),l&&(c=l.scrollX,v=l.scrollY)},m=T=>{if(T.touches.length>0){const F=y(T.touches[0]);F&&([g.value,p.value]=F,b.value="touch")}},w=()=>{if(!f||!l)return;const T=y(f);f instanceof MouseEvent&&T&&(g.value=T[0]+l.scrollX-c,p.value=T[1]+l.scrollY-v)},E=()=>{g.value=a.x,p.value=a.y},k=i?T=>i(()=>h(T),{}):T=>h(T),_=i?T=>i(()=>m(T),{}):T=>m(T),A=i?()=>i(()=>w(),{}):()=>w();if(u){const T={passive:!0};O(u,["mousemove","dragover"],k,T),n&&t!=="movement"&&(O(u,["touchstart","touchmove"],_,T),o&&O(u,"touchend",E,T)),s&&t==="page"&&O(l,"scroll",A,{passive:!0})}return{x:g,y:p,sourceType:b}}function nt(e,t={}){const{handleOutside:n=!0,window:o=I}=t,a=t.type||"page",{x:l,y:u,sourceType:s}=tt(t),i=r.ref(e??o?.document.body),f=r.ref(0),c=r.ref(0),v=r.ref(0),g=r.ref(0),p=r.ref(0),b=r.ref(0),y=r.ref(!0);let h=()=>{};return o&&(h=r.watch([i,l,u],()=>{const m=N(i);if(!m||!(m instanceof Element))return;const{left:w,top:E,width:k,height:_}=m.getBoundingClientRect();v.value=w+(a==="page"?o.pageXOffset:0),g.value=E+(a==="page"?o.pageYOffset:0),p.value=_,b.value=k;const A=l.value-v.value,T=u.value-g.value;y.value=k===0||_===0||A<0||T<0||A>k||T>_,(n||!y.value)&&(f.value=A,c.value=T)},{immediate:!0}),O(document,"mouseleave",()=>{y.value=!0})),{x:l,y:u,sourceType:s,elementX:f,elementY:c,elementPositionX:v,elementPositionY:g,elementHeight:p,elementWidth:b,isOutside:y,stop:h}}function go(e={}){const{touch:t=!0,drag:n=!0,capture:o=!1,initialValue:a=!1,window:l=I}=e,u=r.ref(a),s=r.ref(null);if(!l)return{pressed:u,sourceType:s};const i=v=>()=>{u.value=!0,s.value=v},f=()=>{u.value=!1,s.value=null},c=r.computed(()=>N(e.target)||l);return O(c,"mousedown",i("mouse"),{passive:!0,capture:o}),O(l,"mouseleave",f,{passive:!0,capture:o}),O(l,"mouseup",f,{passive:!0,capture:o}),n&&(O(c,"dragstart",i("mouse"),{passive:!0,capture:o}),O(l,"drop",f,{passive:!0,capture:o}),O(l,"dragend",f,{passive:!0,capture:o})),t&&(O(c,"touchstart",i("touch"),{passive:!0,capture:o}),O(l,"touchend",f,{passive:!0,capture:o}),O(l,"touchcancel",f,{passive:!0,capture:o})),{pressed:u,sourceType:s}}function ho(e={}){const{window:t=I}=e,n=t?.navigator,o=x(()=>n&&"language"in n),a=r.ref(n?.language);return O(t,"languagechange",()=>{n&&(a.value=n.language)}),{isSupported:o,language:a}}function ot(e={}){const{window:t=I}=e,n=t?.navigator,o=x(()=>n&&"connection"in n),a=r.ref(!0),l=r.ref(!1),u=r.ref(void 0),s=r.ref(void 0),i=r.ref(void 0),f=r.ref(void 0),c=r.ref(void 0),v=r.ref(void 0),g=r.ref("unknown"),p=o.value&&n.connection;function b(){n&&(a.value=n.onLine,u.value=a.value?void 0:Date.now(),s.value=a.value?Date.now():void 0,p&&(i.value=p.downlink,f.value=p.downlinkMax,v.value=p.effectiveType,c.value=p.rtt,l.value=p.saveData,g.value=p.type))}return t&&(O(t,"offline",()=>{a.value=!1,u.value=Date.now()}),O(t,"online",()=>{a.value=!0,s.value=Date.now()})),p&&O(p,"change",b,!1),b(),{isSupported:r.readonly(o),isOnline:r.readonly(a),saveData:r.readonly(l),offlineAt:r.readonly(u),onlineAt:r.readonly(s),downlink:r.readonly(i),downlinkMax:r.readonly(f),effectiveType:r.readonly(v),rtt:r.readonly(c),type:r.readonly(g)}}function lt(e={}){const{controls:t=!1,interval:n="requestAnimationFrame"}=e,o=r.ref(new Date),a=()=>o.value=new Date,l=n==="requestAnimationFrame"?ee(a,{immediate:!0}):d.useIntervalFn(a,n,{immediate:!0});return t?{now:o,...l}:o}function wo(e){const t=r.ref(),n=()=>{t.value&&URL.revokeObjectURL(t.value),t.value=void 0};return r.watch(()=>d.toValue(e),o=>{n(),o&&(t.value=URL.createObjectURL(o))},{immediate:!0}),d.tryOnScopeDispose(n),r.readonly(t)}function rt(e,t,n){if(typeof e=="function"||r.isReadonly(e))return r.computed(()=>d.clamp(d.toValue(e),d.toValue(t),d.toValue(n)));const o=r.ref(e);return r.computed({get(){return o.value=d.clamp(o.value,d.toValue(t),d.toValue(n))},set(a){o.value=d.clamp(a,d.toValue(t),d.toValue(n))}})}function bo(e){const{total:t=Number.POSITIVE_INFINITY,pageSize:n=10,page:o=1,onPageChange:a=d.noop,onPageSizeChange:l=d.noop,onPageCountChange:u=d.noop}=e,s=rt(n,1,Number.POSITIVE_INFINITY),i=r.computed(()=>Math.max(1,Math.ceil(d.toValue(t)/d.toValue(s)))),f=rt(o,1,i),c=r.computed(()=>f.value===1),v=r.computed(()=>f.value===i.value);r.isRef(o)&&d.syncRef(o,f,{direction:r.isReadonly(o)?"ltr":"both"}),r.isRef(n)&&d.syncRef(n,s,{direction:r.isReadonly(n)?"ltr":"both"});function g(){f.value--}function p(){f.value++}const b={currentPage:f,currentPageSize:s,pageCount:i,isFirstPage:c,isLastPage:v,prev:g,next:p};return r.watch(f,()=>{a(r.reactive(b))}),r.watch(s,()=>{l(r.reactive(b))}),r.watch(i,()=>{u(r.reactive(b))}),b}function So(e={}){const{isOnline:t}=ot(e);return t}function Eo(e={}){const{window:t=I}=e,n=r.ref(!1),o=a=>{if(!t)return;a=a||t.event;const l=a.relatedTarget||a.toElement;n.value=!l};return t&&(O(t,"mouseout",o,{passive:!0}),O(t.document,"mouseleave",o,{passive:!0}),O(t.document,"mouseenter",o,{passive:!0})),n}function at(e={}){const{window:t=I}=e,n=x(()=>t&&"screen"in t&&"orientation"in t.screen),o=n.value?t.screen.orientation:{},a=r.ref(o.type),l=r.ref(o.angle||0);return n.value&&O(t,"orientationchange",()=>{a.value=o.type,l.value=o.angle}),{isSupported:n,orientation:a,angle:l,lockOrientation:i=>n.value&&typeof o.lock=="function"?o.lock(i):Promise.reject(new Error("Not supported")),unlockOrientation:()=>{n.value&&typeof o.unlock=="function"&&o.unlock()}}}function To(e,t={}){const{deviceOrientationTiltAdjust:n=h=>h,deviceOrientationRollAdjust:o=h=>h,mouseTiltAdjust:a=h=>h,mouseRollAdjust:l=h=>h,window:u=I}=t,s=r.reactive(qe({window:u})),i=r.reactive(at({window:u})),{elementX:f,elementY:c,elementWidth:v,elementHeight:g}=nt(e,{handleOutside:!1,window:u}),p=r.computed(()=>s.isSupported&&(s.alpha!=null&&s.alpha!==0||s.gamma!=null&&s.gamma!==0)?"deviceOrientation":"mouse"),b=r.computed(()=>{if(p.value==="deviceOrientation"){let h;switch(i.orientation){case"landscape-primary":h=s.gamma/90;break;case"landscape-secondary":h=-s.gamma/90;break;case"portrait-primary":h=-s.beta/90;break;case"portrait-secondary":h=s.beta/90;break;default:h=-s.beta/90}return o(h)}else{const h=-(c.value-g.value/2)/g.value;return l(h)}}),y=r.computed(()=>{if(p.value==="deviceOrientation"){let h;switch(i.orientation){case"landscape-primary":h=s.beta/90;break;case"landscape-secondary":h=-s.beta/90;break;case"portrait-primary":h=s.gamma/90;break;case"portrait-secondary":h=-s.gamma/90;break;default:h=s.gamma/90}return n(h)}else{const h=(f.value-v.value/2)/v.value;return a(h)}});return{roll:b,tilt:y,source:p}}function ko(e=Be()){const t=r.shallowRef(),n=()=>{const o=N(e);o&&(t.value=o.parentElement)};return d.tryOnMounted(n),r.watch(()=>d.toValue(e),n),t}function Oo(e,t){const{window:n=I,immediate:o=!0,...a}=e,l=x(()=>n&&"PerformanceObserver"in n);let u;const s=()=>{u?.disconnect()},i=()=>{l.value&&(s(),u=new PerformanceObserver(t),u.observe(a))};return d.tryOnScopeDispose(s),o&&i(),{isSupported:l,start:i,stop:s}}const ut={x:0,y:0,pointerId:0,pressure:0,tiltX:0,tiltY:0,width:0,height:0,twist:0,pointerType:null},_o=Object.keys(ut);function Ro(e={}){const{target:t=I}=e,n=r.ref(!1),o=r.ref(e.initialValue||{});Object.assign(o.value,ut,o.value);const a=l=>{n.value=!0,!(e.pointerTypes&&!e.pointerTypes.includes(l.pointerType))&&(o.value=d.objectPick(l,_o,!1))};if(t){const l={passive:!0};O(t,["pointerdown","pointermove","pointerup"],a,l),O(t,"pointerleave",()=>n.value=!1,l)}return{...d.toRefs(o),isInside:n}}function Fo(e,t={}){const{document:n=B}=t,o=x(()=>n&&"pointerLockElement"in n),a=r.ref(),l=r.ref();let u;o.value&&(O(n,"pointerlockchange",()=>{var f;const c=(f=n.pointerLockElement)!=null?f:a.value;u&&c===u&&(a.value=n.pointerLockElement,a.value||(u=l.value=null))}),O(n,"pointerlockerror",()=>{var f;const c=(f=n.pointerLockElement)!=null?f:a.value;if(u&&c===u){const v=n.pointerLockElement?"release":"acquire";throw new Error(`Failed to ${v} pointer lock.`)}}));async function s(f){var c;if(!o.value)throw new Error("Pointer Lock API is not supported by your browser.");if(l.value=f instanceof Event?f.currentTarget:null,u=f instanceof Event?(c=N(e))!=null?c:l.value:N(f),!u)throw new Error("Target element undefined.");return u.requestPointerLock(),await d.until(a).toBe(u)}async function i(){return a.value?(n.exitPointerLock(),await d.until(a).toBeNull(),!0):!1}return{isSupported:o,element:a,triggerElement:l,lock:s,unlock:i}}function Po(e,t={}){const n=d.toRef(e),{threshold:o=50,onSwipe:a,onSwipeEnd:l,onSwipeStart:u,disableTextSelect:s=!1}=t,i=r.reactive({x:0,y:0}),f=(T,F)=>{i.x=T,i.y=F},c=r.reactive({x:0,y:0}),v=(T,F)=>{c.x=T,c.y=F},g=r.computed(()=>i.x-c.x),p=r.computed(()=>i.y-c.y),{max:b,abs:y}=Math,h=r.computed(()=>b(y(g.value),y(p.value))>=o),m=r.ref(!1),w=r.ref(!1),E=r.computed(()=>h.value?y(g.value)>y(p.value)?g.value>0?"left":"right":p.value>0?"up":"down":"none"),k=T=>{var F,C,P;const R=T.buttons===0,V=T.buttons===1;return(P=(C=(F=t.pointerTypes)==null?void 0:F.includes(T.pointerType))!=null?C:R||V)!=null?P:!0},_=[O(e,"pointerdown",T=>{if(!k(T))return;w.value=!0;const F=T.target;F?.setPointerCapture(T.pointerId);const{clientX:C,clientY:P}=T;f(C,P),v(C,P),u?.(T)}),O(e,"pointermove",T=>{if(!k(T)||!w.value)return;const{clientX:F,clientY:C}=T;v(F,C),!m.value&&h.value&&(m.value=!0),m.value&&a?.(T)}),O(e,"pointerup",T=>{k(T)&&(m.value&&l?.(T,E.value),w.value=!1,m.value=!1)})];d.tryOnMounted(()=>{var T,F,C,P,R,V,L,$;(F=(T=n.value)==null?void 0:T.style)==null||F.setProperty("touch-action","none"),s&&((P=(C=n.value)==null?void 0:C.style)==null||P.setProperty("-webkit-user-select","none"),(V=(R=n.value)==null?void 0:R.style)==null||V.setProperty("-ms-user-select","none"),($=(L=n.value)==null?void 0:L.style)==null||$.setProperty("user-select","none"))});const A=()=>_.forEach(T=>T());return{isSwiping:r.readonly(m),direction:r.readonly(E),posStart:r.readonly(i),posEnd:r.readonly(c),distanceX:g,distanceY:p,stop:A}}function Co(e){const t=q("(prefers-color-scheme: light)",e),n=q("(prefers-color-scheme: dark)",e);return r.computed(()=>n.value?"dark":t.value?"light":"no-preference")}function Vo(e){const t=q("(prefers-contrast: more)",e),n=q("(prefers-contrast: less)",e),o=q("(prefers-contrast: custom)",e);return r.computed(()=>t.value?"more":n.value?"less":o.value?"custom":"no-preference")}function Ao(e={}){const{window:t=I}=e;if(!t)return r.ref(["en"]);const n=t.navigator,o=r.ref(n.languages);return O(t,"languagechange",()=>{o.value=n.languages}),o}function Io(e){const t=q("(prefers-reduced-motion: reduce)",e);return r.computed(()=>t.value?"reduce":"no-preference")}function Mo(e,t){const n=r.shallowRef(t);return r.watch(d.toRef(e),(o,a)=>{n.value=a},{flush:"sync"}),r.readonly(n)}const st="--vueuse-safe-area-top",it="--vueuse-safe-area-right",ct="--vueuse-safe-area-bottom",ft="--vueuse-safe-area-left";function Lo(){const e=r.ref(""),t=r.ref(""),n=r.ref(""),o=r.ref("");if(d.isClient){const l=ue(st),u=ue(it),s=ue(ct),i=ue(ft);l.value="env(safe-area-inset-top, 0px)",u.value="env(safe-area-inset-right, 0px)",s.value="env(safe-area-inset-bottom, 0px)",i.value="env(safe-area-inset-left, 0px)",a(),O("resize",d.useDebounceFn(a))}function a(){e.value=be(st),t.value=be(it),n.value=be(ct),o.value=be(ft)}return{top:e,right:t,bottom:n,left:o,update:a}}function be(e){return getComputedStyle(document.documentElement).getPropertyValue(e)}function No(e,t=d.noop,n={}){const{immediate:o=!0,manual:a=!1,type:l="text/javascript",async:u=!0,crossOrigin:s,referrerPolicy:i,noModule:f,defer:c,document:v=B,attrs:g={}}=n,p=r.ref(null);let b=null;const y=w=>new Promise((E,k)=>{const _=F=>(p.value=F,E(F),F);if(!v){E(!1);return}let A=!1,T=v.querySelector(`script[src="${d.toValue(e)}"]`);T?T.hasAttribute("data-loaded")&&_(T):(T=v.createElement("script"),T.type=l,T.async=u,T.src=d.toValue(e),c&&(T.defer=c),s&&(T.crossOrigin=s),f&&(T.noModule=f),i&&(T.referrerPolicy=i),Object.entries(g).forEach(([F,C])=>T?.setAttribute(F,C)),A=!0),T.addEventListener("error",F=>k(F)),T.addEventListener("abort",F=>k(F)),T.addEventListener("load",()=>{T.setAttribute("data-loaded","true"),t(T),_(T)}),A&&(T=v.head.appendChild(T)),w||_(T)}),h=(w=!0)=>(b||(b=y(w)),b),m=()=>{if(!v)return;b=null,p.value&&(p.value=null);const w=v.querySelector(`script[src="${d.toValue(e)}"]`);w&&v.head.removeChild(w)};return o&&!a&&d.tryOnMounted(h),a||d.tryOnUnmounted(m),{scriptTag:p,load:h,unload:m}}function dt(e){const t=window.getComputedStyle(e);if(t.overflowX==="scroll"||t.overflowY==="scroll"||t.overflowX==="auto"&&e.clientWidth<e.scrollWidth||t.overflowY==="auto"&&e.clientHeight<e.scrollHeight)return!0;{const n=e.parentNode;return!n||n.tagName==="BODY"?!1:dt(n)}}function xo(e){const t=e||window.event,n=t.target;return dt(n)?!1:t.touches.length>1?!0:(t.preventDefault&&t.preventDefault(),!1)}const Pe=new WeakMap;function $o(e,t=!1){const n=r.ref(t);let o=null,a="";r.watch(d.toRef(e),s=>{const i=we(d.toValue(s));if(i){const f=i;if(Pe.get(f)||Pe.set(f,f.style.overflow),f.style.overflow!=="hidden"&&(a=f.style.overflow),f.style.overflow==="hidden")return n.value=!0;if(n.value)return f.style.overflow="hidden"}},{immediate:!0});const l=()=>{const s=we(d.toValue(e));!s||n.value||(d.isIOS&&(o=O(s,"touchmove",i=>{xo(i)},{passive:!1})),s.style.overflow="hidden",n.value=!0)},u=()=>{const s=we(d.toValue(e));!s||!n.value||(d.isIOS&&o?.(),s.style.overflow=a,Pe.delete(s),n.value=!1)};return d.tryOnScopeDispose(u),r.computed({get(){return n.value},set(s){s?l():u()}})}function Wo(e,t,n={}){const{window:o=I}=n;return me(e,t,o?.sessionStorage,n)}function Uo(e={},t={}){const{navigator:n=H}=t,o=n,a=x(()=>o&&"canShare"in o);return{isSupported:a,share:async(u={})=>{if(a.value){const s={...d.toValue(e),...d.toValue(u)};let i=!0;if(s.files&&o.canShare&&(i=o.canShare({files:s.files})),i)return o.share(s)}}}}const Ho=(e,t)=>e.sort(t),Se=(e,t)=>e-t;function Bo(...e){var t,n,o,a;const[l]=e;let u=Se,s={};e.length===2?typeof e[1]=="object"?(s=e[1],u=(t=s.compareFn)!=null?t:Se):u=(n=e[1])!=null?n:Se:e.length>2&&(u=(o=e[1])!=null?o:Se,s=(a=e[2])!=null?a:{});const{dirty:i=!1,sortFn:f=Ho}=s;return i?(r.watchEffect(()=>{const c=f(d.toValue(l),u);r.isRef(l)?l.value=c:l.splice(0,l.length,...c)}),l):r.computed(()=>f([...d.toValue(l)],u))}function jo(e={}){const{interimResults:t=!0,continuous:n=!0,maxAlternatives:o=1,window:a=I}=e,l=d.toRef(e.lang||"en-US"),u=r.ref(!1),s=r.ref(!1),i=r.ref(""),f=r.shallowRef(void 0),c=(h=!u.value)=>{u.value=h},v=()=>{u.value=!0},g=()=>{u.value=!1},p=a&&(a.SpeechRecognition||a.webkitSpeechRecognition),b=x(()=>p);let y;return b.value&&(y=new p,y.continuous=n,y.interimResults=t,y.lang=d.toValue(l),y.maxAlternatives=o,y.onstart=()=>{s.value=!1},r.watch(l,h=>{y&&!u.value&&(y.lang=h)}),y.onresult=h=>{const m=h.results[h.resultIndex],{transcript:w}=m[0];s.value=m.isFinal,i.value=w,f.value=void 0},y.onerror=h=>{f.value=h},y.onend=()=>{u.value=!1,y.lang=d.toValue(l)},r.watch(u,()=>{u.value?y.start():y.stop()})),d.tryOnScopeDispose(()=>{u.value=!1}),{isSupported:b,isListening:u,isFinal:s,recognition:y,result:i,error:f,toggle:c,start:v,stop:g}}function zo(e,t={}){const{pitch:n=1,rate:o=1,volume:a=1,window:l=I}=t,u=l&&l.speechSynthesis,s=x(()=>u),i=r.ref(!1),f=r.ref("init"),c=d.toRef(e||""),v=d.toRef(t.lang||"en-US"),g=r.shallowRef(void 0),p=(w=!i.value)=>{i.value=w},b=w=>{w.lang=d.toValue(v),w.voice=d.toValue(t.voice)||null,w.pitch=d.toValue(n),w.rate=d.toValue(o),w.volume=a,w.onstart=()=>{i.value=!0,f.value="play"},w.onpause=()=>{i.value=!1,f.value="pause"},w.onresume=()=>{i.value=!0,f.value="play"},w.onend=()=>{i.value=!1,f.value="end"},w.onerror=E=>{g.value=E}},y=r.computed(()=>{i.value=!1,f.value="init";const w=new SpeechSynthesisUtterance(c.value);return b(w),w}),h=()=>{u.cancel(),y&&u.speak(y.value)},m=()=>{u.cancel(),i.value=!1};return s.value&&(b(y.value),r.watch(v,w=>{y.value&&!i.value&&(y.value.lang=w)}),t.voice&&r.watch(t.voice,()=>{u.cancel()}),r.watch(i,()=>{i.value?u.resume():u.pause()})),d.tryOnScopeDispose(()=>{i.value=!1}),{isSupported:s,isPlaying:i,status:f,utterance:y,error:g,stop:m,toggle:p,speak:h}}function qo(e,t){const n=r.ref(e),o=r.computed(()=>Array.isArray(n.value)?n.value:Object.keys(n.value)),a=r.ref(o.value.indexOf(t??o.value[0])),l=r.computed(()=>c(a.value)),u=r.computed(()=>a.value===0),s=r.computed(()=>a.value===o.value.length-1),i=r.computed(()=>o.value[a.value+1]),f=r.computed(()=>o.value[a.value-1]);function c(_){return Array.isArray(n.value)?n.value[_]:n.value[o.value[_]]}function v(_){if(o.value.includes(_))return c(o.value.indexOf(_))}function g(_){o.value.includes(_)&&(a.value=o.value.indexOf(_))}function p(){s.value||a.value++}function b(){u.value||a.value--}function y(_){k(_)&&g(_)}function h(_){return o.value.indexOf(_)===a.value+1}function m(_){return o.value.indexOf(_)===a.value-1}function w(_){return o.value.indexOf(_)===a.value}function E(_){return a.value<o.value.indexOf(_)}function k(_){return a.value>o.value.indexOf(_)}return{steps:n,stepNames:o,index:a,current:l,next:i,previous:f,isFirst:u,isLast:s,at:c,get:v,goTo:g,goToNext:p,goToPrevious:b,goBackTo:y,isNext:h,isPrevious:m,isCurrent:w,isBefore:E,isAfter:k}}function Go(e,t,n,o={}){var a;const{flush:l="pre",deep:u=!0,listenToStorageChanges:s=!0,writeDefaults:i=!0,mergeDefaults:f=!1,shallow:c,window:v=I,eventFilter:g,onError:p=E=>{console.error(E)}}=o,b=d.toValue(t),y=Ue(b),h=(c?r.shallowRef:r.ref)(t),m=(a=o.serializer)!=null?a:ke[y];if(!n)try{n=ye("getDefaultStorageAsync",()=>{var E;return(E=I)==null?void 0:E.localStorage})()}catch(E){p(E)}async function w(E){if(!(!n||E&&E.key!==e))try{const k=E?E.newValue:await n.getItem(e);if(k==null)h.value=b,i&&b!==null&&await n.setItem(e,await m.write(b));else if(f){const _=await m.read(k);typeof f=="function"?h.value=f(_,b):y==="object"&&!Array.isArray(_)?h.value={...b,..._}:h.value=_}else h.value=await m.read(k)}catch(k){p(k)}}return w(),v&&s&&O(v,"storage",E=>Promise.resolve().then(()=>w(E))),n&&d.watchWithFilter(h,async()=>{try{h.value==null?await n.removeItem(e):await n.setItem(e,await m.write(h.value))}catch(E){p(E)}},{flush:l,deep:u,eventFilter:g}),h}let Yo=0;function Xo(e,t={}){const n=r.ref(!1),{document:o=B,immediate:a=!0,manual:l=!1,id:u=`vueuse_styletag_${++Yo}`}=t,s=r.ref(e);let i=()=>{};const f=()=>{if(!o)return;const v=o.getElementById(u)||o.createElement("style");v.isConnected||(v.id=u,t.media&&(v.media=t.media),o.head.appendChild(v)),!n.value&&(i=r.watch(s,g=>{v.textContent=g},{immediate:!0}),n.value=!0)},c=()=>{!o||!n.value||(i(),o.head.removeChild(o.getElementById(u)),n.value=!1)};return a&&!l&&d.tryOnMounted(f),l||d.tryOnScopeDispose(c),{id:u,css:s,unload:c,load:f,isLoaded:r.readonly(n)}}function Ko(e,t={}){const{threshold:n=50,onSwipe:o,onSwipeEnd:a,onSwipeStart:l,passive:u=!0,window:s=I}=t,i=r.reactive({x:0,y:0}),f=r.reactive({x:0,y:0}),c=r.computed(()=>i.x-f.x),v=r.computed(()=>i.y-f.y),{max:g,abs:p}=Math,b=r.computed(()=>g(p(c.value),p(v.value))>=n),y=r.ref(!1),h=r.computed(()=>b.value?p(c.value)>p(v.value)?c.value>0?"left":"right":v.value>0?"up":"down":"none"),m=C=>[C.touches[0].clientX,C.touches[0].clientY],w=(C,P)=>{i.x=C,i.y=P},E=(C,P)=>{f.x=C,f.y=P};let k;const _=Jo(s?.document);u?k=_?{passive:!0}:{capture:!1}:k=_?{passive:!1,capture:!0}:{capture:!0};const A=C=>{y.value&&a?.(C,h.value),y.value=!1},T=[O(e,"touchstart",C=>{if(C.touches.length!==1)return;const[P,R]=m(C);w(P,R),E(P,R),l?.(C)},k),O(e,"touchmove",C=>{if(C.touches.length!==1)return;const[P,R]=m(C);E(P,R),k.capture&&!k.passive&&Math.abs(c.value)>Math.abs(v.value)&&C.preventDefault(),!y.value&&b.value&&(y.value=!0),y.value&&o?.(C)},k),O(e,["touchend","touchcancel"],A,k)];return{isPassiveEventSupported:_,isSwiping:y,direction:h,coordsStart:i,coordsEnd:f,lengthX:c,lengthY:v,stop:()=>T.forEach(C=>C())}}function Jo(e){if(!e)return!1;let t=!1;const n={get passive(){return t=!0,!1}};return e.addEventListener("x",d.noop,n),e.removeEventListener("x",d.noop),t}function Qo(){const e=r.ref([]);return e.value.set=t=>{t&&e.value.push(t)},r.onBeforeUpdate(()=>{e.value.length=0}),e}function Zo(e={}){const{document:t=B,selector:n="html",observe:o=!1,initialValue:a="ltr"}=e;function l(){var s,i;return(i=(s=t?.querySelector(n))==null?void 0:s.getAttribute("dir"))!=null?i:a}const u=r.ref(l());return d.tryOnMounted(()=>u.value=l()),o&&t&&oe(t.querySelector(n),()=>u.value=l(),{attributes:!0}),r.computed({get(){return u.value},set(s){var i,f;u.value=s,t&&(u.value?(i=t.querySelector(n))==null||i.setAttribute("dir",u.value):(f=t.querySelector(n))==null||f.removeAttribute("dir"))}})}function Do(e){var t;const n=(t=e.rangeCount)!=null?t:0;return Array.from({length:n},(o,a)=>e.getRangeAt(a))}function el(e={}){const{window:t=I}=e,n=r.ref(null),o=r.computed(()=>{var s,i;return(i=(s=n.value)==null?void 0:s.toString())!=null?i:""}),a=r.computed(()=>n.value?Do(n.value):[]),l=r.computed(()=>a.value.map(s=>s.getBoundingClientRect()));function u(){n.value=null,t&&(n.value=t.getSelection())}return t&&O(t.document,"selectionchange",u),{text:o,rects:l,ranges:a,selection:n}}function tl(e){var t;const n=r.ref(e?.element),o=r.ref(e?.input),a=(t=e?.styleProp)!=null?t:"height",l=r.ref(1),u=r.ref(0);function s(){var i;if(!n.value)return;let f="";n.value.style[a]="1px",l.value=(i=n.value)==null?void 0:i.scrollHeight;const c=d.toValue(e?.styleTarget);c?c.style[a]=`${l.value}px`:f=`${l.value}px`,n.value.style[a]=f}return r.watch([o,n],()=>r.nextTick(s),{immediate:!0}),r.watch(l,()=>{var i;return(i=e?.onResize)==null?void 0:i.call(e)}),ge(n,([{contentRect:i}])=>{u.value!==i.width&&(u.value=i.width,s())}),e?.watch&&r.watch(e.watch,s,{immediate:!0,deep:!0}),{textarea:n,input:o,triggerResize:s}}function nl(e,t={}){const{throttle:n=200,trailing:o=!0}=t,a=d.throttleFilter(n,o);return{..._e(e,{...t,eventFilter:a})}}const ol=[{max:6e4,value:1e3,name:"second"},{max:276e4,value:6e4,name:"minute"},{max:72e6,value:36e5,name:"hour"},{max:5184e5,value:864e5,name:"day"},{max:24192e5,value:6048e5,name:"week"},{max:28512e6,value:2592e6,name:"month"},{max:Number.POSITIVE_INFINITY,value:31536e6,name:"year"}],ll={justNow:"just now",past:e=>e.match(/\d/)?`${e} ago`:e,future:e=>e.match(/\d/)?`in ${e}`:e,month:(e,t)=>e===1?t?"last month":"next month":`${e} month${e>1?"s":""}`,year:(e,t)=>e===1?t?"last year":"next year":`${e} year${e>1?"s":""}`,day:(e,t)=>e===1?t?"yesterday":"tomorrow":`${e} day${e>1?"s":""}`,week:(e,t)=>e===1?t?"last week":"next week":`${e} week${e>1?"s":""}`,hour:e=>`${e} hour${e>1?"s":""}`,minute:e=>`${e} minute${e>1?"s":""}`,second:e=>`${e} second${e>1?"s":""}`,invalid:""};function rl(e){return e.toISOString().slice(0,10)}function al(e,t={}){const{controls:n=!1,updateInterval:o=3e4}=t,{now:a,...l}=lt({interval:o,controls:!0}),u=r.computed(()=>vt(new Date(d.toValue(e)),t,d.toValue(a)));return n?{timeAgo:u,...l}:u}function vt(e,t={},n=Date.now()){var o;const{max:a,messages:l=ll,fullDateFormatter:u=rl,units:s=ol,showSecond:i=!1,rounding:f="round"}=t,c=typeof f=="number"?h=>+h.toFixed(f):Math[f],v=+n-+e,g=Math.abs(v);function p(h,m){return c(Math.abs(h)/m.value)}function b(h,m){const w=p(h,m),E=h>0,k=y(m.name,w,E);return y(E?"past":"future",k,E)}function y(h,m,w){const E=l[h];return typeof E=="function"?E(m,w):E.replace("{0}",m.toString())}if(g<6e4&&!i)return l.justNow;if(typeof a=="number"&&g>a)return u(new Date(e));if(typeof a=="string"){const h=(o=s.find(m=>m.name===a))==null?void 0:o.max;if(h&&g>h)return u(new Date(e))}for(const[h,m]of s.entries()){if(p(v,m)<=0&&s[h-1])return b(v,s[h-1]);if(g<m.max)return b(v,m)}return l.invalid}function ul(e,t,n){const{start:o}=d.useTimeoutFn(l,t,{immediate:!1}),a=r.ref(!1);async function l(){a.value&&(await e(),o())}function u(){a.value||(a.value=!0,l())}function s(){a.value=!1}return n?.immediate&&u(),d.tryOnScopeDispose(s),{isActive:a,pause:s,resume:u}}function sl(e={}){const{controls:t=!1,offset:n=0,immediate:o=!0,interval:a="requestAnimationFrame",callback:l}=e,u=r.ref(d.timestamp()+n),s=()=>u.value=d.timestamp()+n,i=l?()=>{s(),l(u.value)}:s,f=a==="requestAnimationFrame"?ee(i,{immediate:o}):d.useIntervalFn(i,a,{immediate:o});return t?{timestamp:u,...f}:u}function il(e=null,t={}){var n,o,a;const{document:l=B,restoreOnUnmount:u=v=>v}=t,s=(n=l?.title)!=null?n:"",i=d.toRef((o=e??l?.title)!=null?o:null),f=e&&typeof e=="function";function c(v){if(!("titleTemplate"in t))return v;const g=t.titleTemplate||"%s";return typeof g=="function"?g(v):d.toValue(g).replace(/%s/g,v)}return r.watch(i,(v,g)=>{v!==g&&l&&(l.title=c(typeof v=="string"?v:""))},{immediate:!0}),t.observe&&!t.titleTemplate&&l&&!f&&oe((a=l.head)==null?void 0:a.querySelector("title"),()=>{l&&l.title!==i.value&&(i.value=c(l.title))},{childList:!0}),d.tryOnBeforeUnmount(()=>{if(u){const v=u(s,i.value||"");v!=null&&l&&(l.title=v)}}),i}const cl={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]},fl=Object.assign({},{linear:d.identity},cl);function dl([e,t,n,o]){const a=(c,v)=>1-3*v+3*c,l=(c,v)=>3*v-6*c,u=c=>3*c,s=(c,v,g)=>((a(v,g)*c+l(v,g))*c+u(v))*c,i=(c,v,g)=>3*a(v,g)*c*c+2*l(v,g)*c+u(v),f=c=>{let v=c;for(let g=0;g<4;++g){const p=i(v,e,n);if(p===0)return v;const b=s(v,e,n)-c;v-=b/p}return v};return c=>e===t&&n===o?c:s(f(c),t,o)}function pt(e,t,n){return e+n*(t-e)}function Ce(e){return(typeof e=="number"?[e]:e)||[]}function yt(e,t,n,o={}){var a,l;const u=d.toValue(t),s=d.toValue(n),i=Ce(u),f=Ce(s),c=(a=d.toValue(o.duration))!=null?a:1e3,v=Date.now(),g=Date.now()+c,p=typeof o.transition=="function"?o.transition:(l=d.toValue(o.transition))!=null?l:d.identity,b=typeof p=="function"?p:dl(p);return new Promise(y=>{e.value=u;const h=()=>{var m;if((m=o.abort)!=null&&m.call(o)){y();return}const w=Date.now(),E=b((w-v)/c),k=Ce(e.value).map((_,A)=>pt(i[A],f[A],E));Array.isArray(e.value)?e.value=k.map((_,A)=>{var T,F;return pt((T=i[A])!=null?T:0,(F=f[A])!=null?F:0,E)}):typeof e.value=="number"&&(e.value=k[0]),w<g?requestAnimationFrame(h):(e.value=s,y())};h()})}function vl(e,t={}){let n=0;const o=()=>{const l=d.toValue(e);return typeof l=="number"?l:l.map(d.toValue)},a=r.ref(o());return r.watch(o,async l=>{var u,s;if(d.toValue(t.disabled))return;const i=++n;if(t.delay&&await d.promiseTimeout(d.toValue(t.delay)),i!==n)return;const f=Array.isArray(l)?l.map(d.toValue):d.toValue(l);(u=t.onStarted)==null||u.call(t),await yt(a,a.value,f,{...t,abort:()=>{var c;return i!==n||((c=t.abort)==null?void 0:c.call(t))}}),(s=t.onFinished)==null||s.call(t)},{deep:!0}),r.watch(()=>d.toValue(t.disabled),l=>{l&&(n++,a.value=o())}),d.tryOnScopeDispose(()=>{n++}),r.computed(()=>d.toValue(t.disabled)?o():a.value)}function pl(e="history",t={}){const{initialValue:n={},removeNullishValues:o=!0,removeFalsyValues:a=!1,write:l=!0,window:u=I}=t;if(!u)return r.reactive(n);const s=r.reactive({});function i(){if(e==="history")return u.location.search||"";if(e==="hash"){const m=u.location.hash||"",w=m.indexOf("?");return w>0?m.slice(w):""}else return(u.location.hash||"").replace(/^#/,"")}function f(m){const w=m.toString();if(e==="history")return`${w?`?${w}`:""}${u.location.hash||""}`;if(e==="hash-params")return`${u.location.search||""}${w?`#${w}`:""}`;const E=u.location.hash||"#",k=E.indexOf("?");return k>0?`${u.location.search||""}${E.slice(0,k)}${w?`?${w}`:""}`:`${u.location.search||""}${E}${w?`?${w}`:""}`}function c(){return new URLSearchParams(i())}function v(m){const w=new Set(Object.keys(s));for(const E of m.keys()){const k=m.getAll(E);s[E]=k.length>1?k:m.get(E)||"",w.delete(E)}Array.from(w).forEach(E=>delete s[E])}const{pause:g,resume:p}=d.pausableWatch(s,()=>{const m=new URLSearchParams("");Object.keys(s).forEach(w=>{const E=s[w];Array.isArray(E)?E.forEach(k=>m.append(w,k)):o&&E==null||a&&!E?m.delete(w):m.set(w,E)}),b(m)},{deep:!0});function b(m,w){g(),w&&v(m),u.history.replaceState(u.history.state,u.document.title,u.location.pathname+f(m)),p()}function y(){l&&b(c(),!0)}O(u,"popstate",y,!1),e!=="history"&&O(u,"hashchange",y,!1);const h=c();return h.keys().next().value?v(h):Object.assign(s,n),s}function yl(e={}){var t,n;const o=r.ref((t=e.enabled)!=null?t:!1),a=r.ref((n=e.autoSwitch)!=null?n:!0),l=r.ref(e.constraints),{navigator:u=H}=e,s=x(()=>{var y;return(y=u?.mediaDevices)==null?void 0:y.getUserMedia}),i=r.shallowRef();function f(y){switch(y){case"video":{if(l.value)return l.value.video||!1;break}case"audio":{if(l.value)return l.value.audio||!1;break}}}async function c(){if(!(!s.value||i.value))return i.value=await u.mediaDevices.getUserMedia({video:f("video"),audio:f("audio")}),i.value}function v(){var y;(y=i.value)==null||y.getTracks().forEach(h=>h.stop()),i.value=void 0}function g(){v(),o.value=!1}async function p(){return await c(),i.value&&(o.value=!0),i.value}async function b(){return v(),await p()}return r.watch(o,y=>{y?c():v()},{immediate:!0}),r.watch(l,()=>{a.value&&i.value&&b()},{immediate:!0}),d.tryOnScopeDispose(()=>{g()}),{isSupported:s,stream:i,start:p,stop:g,restart:b,constraints:l,enabled:o,autoSwitch:a}}function mt(e,t,n,o={}){var a,l,u,s,i;const{clone:f=!1,passive:c=!1,eventName:v,deep:g=!1,defaultValue:p,shouldEmit:b}=o,y=r.getCurrentInstance(),h=n||y?.emit||((a=y?.$emit)==null?void 0:a.bind(y))||((u=(l=y?.proxy)==null?void 0:l.$emit)==null?void 0:u.bind(y?.proxy));let m=v;if(!t)if(r.isVue2){const _=(i=(s=y?.proxy)==null?void 0:s.$options)==null?void 0:i.model;t=_?.value||"value",v||(m=_?.event||"input")}else t="modelValue";m=m||`update:${t.toString()}`;const w=_=>f?typeof f=="function"?f(_):ae(_):_,E=()=>d.isDef(e[t])?w(e[t]):p,k=_=>{b?b(_)&&h(m,_):h(m,_)};if(c){const _=E(),A=r.ref(_);let T=!1;return r.watch(()=>e[t],F=>{T||(T=!0,A.value=w(F),r.nextTick(()=>T=!1))}),r.watch(A,F=>{!T&&(F!==e[t]||g)&&k(F)},{deep:g}),A}else return r.computed({get(){return E()},set(_){k(_)}})}function ml(e,t,n={}){const o={};for(const a in e)o[a]=mt(e,a,t,n);return o}function gl(e){const{pattern:t=[],interval:n=0,navigator:o=H}=e||{},a=x(()=>typeof o<"u"&&"vibrate"in o),l=d.toRef(t);let u;const s=(f=l.value)=>{a.value&&o.vibrate(f)},i=()=>{a.value&&o.vibrate(0),u?.pause()};return n>0&&(u=d.useIntervalFn(s,n,{immediate:!1,immediateCallback:!1})),{isSupported:a,pattern:t,intervalControls:u,vibrate:s,stop:i}}function hl(e,t){const{containerStyle:n,wrapperProps:o,scrollTo:a,calculateRange:l,currentList:u,containerRef:s}="itemHeight"in t?Sl(t,e):bl(t,e);return{list:u,scrollTo:a,containerProps:{ref:s,onScroll:()=>{l()},style:n},wrapperProps:o}}function gt(e){const t=r.ref(null),n=Ye(t),o=r.ref([]),a=r.shallowRef(e);return{state:r.ref({start:0,end:10}),source:a,currentList:o,size:n,containerRef:t}}function ht(e,t,n){return o=>{if(typeof n=="number")return Math.ceil(o/n);const{start:a=0}=e.value;let l=0,u=0;for(let s=a;s<t.value.length;s++){const i=n(s);if(l+=i,u=s,l>o)break}return u-a}}function wt(e,t){return n=>{if(typeof t=="number")return Math.floor(n/t)+1;let o=0,a=0;for(let l=0;l<e.value.length;l++){const u=t(l);if(o+=u,o>=n){a=l;break}}return a+1}}function bt(e,t,n,o,{containerRef:a,state:l,currentList:u,source:s}){return()=>{const i=a.value;if(i){const f=n(e==="vertical"?i.scrollTop:i.scrollLeft),c=o(e==="vertical"?i.clientHeight:i.clientWidth),v=f-t,g=f+c+t;l.value={start:v<0?0:v,end:g>s.value.length?s.value.length:g},u.value=s.value.slice(l.value.start,l.value.end).map((p,b)=>({data:p,index:b+l.value.start}))}}}function St(e,t){return n=>typeof e=="number"?n*e:t.value.slice(0,n).reduce((a,l,u)=>a+e(u),0)}function Et(e,t,n,o){r.watch([e.width,e.height,t,n],()=>{o()})}function Tt(e,t){return r.computed(()=>typeof e=="number"?t.value.length*e:t.value.reduce((n,o,a)=>n+e(a),0))}const wl={horizontal:"scrollLeft",vertical:"scrollTop"};function kt(e,t,n,o){return a=>{o.value&&(o.value[wl[e]]=n(a),t())}}function bl(e,t){const n=gt(t),{state:o,source:a,currentList:l,size:u,containerRef:s}=n,i={overflowX:"auto"},{itemWidth:f,overscan:c=5}=e,v=ht(o,a,f),g=wt(a,f),p=bt("horizontal",c,g,v,n),b=St(f,a),y=r.computed(()=>b(o.value.start)),h=Tt(f,a);Et(u,t,s,p);const m=kt("horizontal",p,b,s),w=r.computed(()=>({style:{height:"100%",width:`${h.value-y.value}px`,marginLeft:`${y.value}px`,display:"flex"}}));return{scrollTo:m,calculateRange:p,wrapperProps:w,containerStyle:i,currentList:l,containerRef:s}}function Sl(e,t){const n=gt(t),{state:o,source:a,currentList:l,size:u,containerRef:s}=n,i={overflowY:"auto"},{itemHeight:f,overscan:c=5}=e,v=ht(o,a,f),g=wt(a,f),p=bt("vertical",c,g,v,n),b=St(f,a),y=r.computed(()=>b(o.value.start)),h=Tt(f,a);Et(u,t,s,p);const m=kt("vertical",p,b,s),w=r.computed(()=>({style:{width:"100%",height:`${h.value-y.value}px`,marginTop:`${y.value}px`}}));return{calculateRange:p,scrollTo:m,containerStyle:i,wrapperProps:w,currentList:l,containerRef:s}}function El(e={}){const{navigator:t=H,document:n=B}=e,o=r.ref(!1),a=r.shallowRef(null),l=Ge({document:n}),u=x(()=>t&&"wakeLock"in t),s=r.computed(()=>!!a.value&&l.value==="visible");u.value&&(O(a,"release",()=>{var v,g;o.value=(g=(v=a.value)==null?void 0:v.type)!=null?g:!1}),d.whenever(()=>l.value==="visible"&&n?.visibilityState==="visible"&&o.value,v=>{o.value=!1,i(v)}));async function i(v){var g;await((g=a.value)==null?void 0:g.release()),a.value=u.value?await t.wakeLock.request(v):null}async function f(v){l.value==="visible"?await i(v):o.value=v}async function c(){o.value=!1;const v=a.value;a.value=null,await v?.release()}return{sentinel:a,isSupported:u,isActive:s,request:f,forceRequest:i,release:c}}function Tl(e={}){const{window:t=I,requestPermissions:n=!0}=e,o=e,a=x(()=>{if(!t||!("Notification"in t))return!1;try{new Notification("")}catch{return!1}return!0}),l=r.ref(a.value&&"permission"in Notification&&Notification.permission==="granted"),u=r.ref(null),s=async()=>{if(a.value)return!l.value&&Notification.permission!=="denied"&&await Notification.requestPermission()==="granted"&&(l.value=!0),l.value},{on:i,trigger:f}=d.createEventHook(),{on:c,trigger:v}=d.createEventHook(),{on:g,trigger:p}=d.createEventHook(),{on:b,trigger:y}=d.createEventHook(),h=async w=>{if(!a.value||!l.value)return;const E=Object.assign({},o,w);return u.value=new Notification(E.title||"",E),u.value.onclick=f,u.value.onshow=v,u.value.onerror=p,u.value.onclose=y,u.value},m=()=>{u.value&&u.value.close(),u.value=null};if(n&&d.tryOnMounted(s),d.tryOnScopeDispose(m),a.value&&t){const w=t.document;O(w,"visibilitychange",E=>{E.preventDefault(),w.visibilityState==="visible"&&m()})}return{isSupported:a,notification:u,ensurePermissions:s,permissionGranted:l,show:h,close:m,onClick:i,onShow:c,onError:g,onClose:b}}const Ot="ping";function Ve(e){return e===!0?{}:e}function kl(e,t={}){const{onConnected:n,onDisconnected:o,onError:a,onMessage:l,immediate:u=!0,autoClose:s=!0,protocols:i=[]}=t,f=r.ref(null),c=r.ref("CLOSED"),v=r.ref(),g=d.toRef(e);let p,b,y=!1,h=0,m=[],w;const E=()=>{if(m.length&&v.value&&c.value==="OPEN"){for(const C of m)v.value.send(C);m=[]}},k=()=>{clearTimeout(w),w=void 0},_=(C=1e3,P)=>{!d.isClient||!v.value||(y=!0,k(),p?.(),v.value.close(C,P),v.value=void 0)},A=(C,P=!0)=>!v.value||c.value!=="OPEN"?(P&&m.push(C),!1):(E(),v.value.send(C),!0),T=()=>{if(y||typeof g.value>"u")return;const C=new WebSocket(g.value,i);v.value=C,c.value="CONNECTING",C.onopen=()=>{c.value="OPEN",h=0,n?.(C),b?.(),E()},C.onclose=P=>{if(c.value="CLOSED",o?.(C,P),!y&&t.autoReconnect&&(v.value==null||C===v.value)){const{retries:R=-1,delay:V=1e3,onFailed:L}=Ve(t.autoReconnect);typeof R=="number"&&(R<0||h<R)?(h+=1,setTimeout(T,V)):typeof R=="function"&&R()?setTimeout(T,V):L?.()}},C.onerror=P=>{a?.(C,P)},C.onmessage=P=>{if(t.heartbeat){k();const{message:R=Ot,responseMessage:V=R}=Ve(t.heartbeat);if(P.data===V)return}f.value=P.data,l?.(C,P)}};if(t.heartbeat){const{message:C=Ot,interval:P=1e3,pongTimeout:R=1e3}=Ve(t.heartbeat),{pause:V,resume:L}=d.useIntervalFn(()=>{A(C,!1),w==null&&(w=setTimeout(()=>{_(),y=!1},R))},P,{immediate:!1});p=V,b=L}s&&(d.isClient&&O("beforeunload",()=>_()),d.tryOnScopeDispose(_));const F=()=>{!d.isClient&&!d.isWorker||(_(),y=!1,h=0,T())};return u&&F(),r.watch(g,F),{data:f,status:c,close:_,send:A,open:F,ws:v}}function Ol(e,t,n){const{window:o=I}=n??{},a=r.ref(null),l=r.shallowRef(),u=(...i)=>{l.value&&l.value.postMessage(...i)},s=function(){l.value&&l.value.terminate()};return o&&(typeof e=="string"?l.value=new Worker(e,t):typeof e=="function"?l.value=e():l.value=e,l.value.onmessage=i=>{a.value=i.data},d.tryOnScopeDispose(()=>{l.value&&l.value.terminate()})),{data:a,post:u,terminate:s,worker:l}}function _l(e,t){if(e.length===0&&t.length===0)return"";const n=e.map(l=>`'${l}'`).toString(),o=t.filter(l=>typeof l=="function").map(l=>{const u=l.toString();return u.trim().startsWith("function")?u:`const ${l.name} = ${u}`}).join(";"),a=`importScripts(${n});`;return`${n.trim()===""?"":a} ${o}`}function Rl(e){return t=>{const n=t.data[0];return Promise.resolve(e.apply(void 0,n)).then(o=>{postMessage(["SUCCESS",o])}).catch(o=>{postMessage(["ERROR",o])})}}function Fl(e,t,n){const o=`${_l(t,n)}; onmessage=(${Rl})(${e})`,a=new Blob([o],{type:"text/javascript"});return URL.createObjectURL(a)}function Pl(e,t={}){const{dependencies:n=[],localDependencies:o=[],timeout:a,window:l=I}=t,u=r.ref(),s=r.ref("PENDING"),i=r.ref({}),f=r.ref(),c=(b="PENDING")=>{u.value&&u.value._url&&l&&(u.value.terminate(),URL.revokeObjectURL(u.value._url),i.value={},u.value=void 0,l.clearTimeout(f.value),s.value=b)};c(),d.tryOnScopeDispose(c);const v=()=>{const b=Fl(e,n,o),y=new Worker(b);return y._url=b,y.onmessage=h=>{const{resolve:m=()=>{},reject:w=()=>{}}=i.value,[E,k]=h.data;switch(E){case"SUCCESS":m(k),c(E);break;default:w(k),c("ERROR");break}},y.onerror=h=>{const{reject:m=()=>{}}=i.value;h.preventDefault(),m(h),c("ERROR")},a&&(f.value=setTimeout(()=>c("TIMEOUT_EXPIRED"),a)),y},g=(...b)=>new Promise((y,h)=>{var m;i.value={resolve:y,reject:h},(m=u.value)==null||m.postMessage([[...b]]),s.value="RUNNING"});return{workerFn:(...b)=>s.value==="RUNNING"?(console.error("[useWebWorkerFn] You can only run one instance of the worker at a time."),Promise.reject()):(u.value=v(),g(...b)),workerStatus:s,workerTerminate:c}}function Cl(e={}){const{window:t=I}=e;if(!t)return r.ref(!1);const n=r.ref(t.document.hasFocus());return O(t,"blur",()=>{n.value=!1}),O(t,"focus",()=>{n.value=!0}),n}function Vl(e={}){const{window:t=I,behavior:n="auto"}=e;if(!t)return{x:r.ref(0),y:r.ref(0)};const o=r.ref(t.scrollX),a=r.ref(t.scrollY),l=r.computed({get(){return o.value},set(s){scrollTo({left:s,behavior:n})}}),u=r.computed({get(){return a.value},set(s){scrollTo({top:s,behavior:n})}});return O(t,"scroll",()=>{o.value=t.scrollX,a.value=t.scrollY},{capture:!1,passive:!0}),{x:l,y:u}}function Al(e={}){const{window:t=I,initialWidth:n=Number.POSITIVE_INFINITY,initialHeight:o=Number.POSITIVE_INFINITY,listenOrientation:a=!0,includeScrollbar:l=!0,type:u="inner"}=e,s=r.ref(n),i=r.ref(o),f=()=>{t&&(u==="outer"?(s.value=t.outerWidth,i.value=t.outerHeight):l?(s.value=t.innerWidth,i.value=t.innerHeight):(s.value=t.document.documentElement.clientWidth,i.value=t.document.documentElement.clientHeight))};if(f(),d.tryOnMounted(f),O("resize",f,{passive:!0}),a){const c=q("(orientation: portrait)");r.watch(c,()=>f())}return{width:s,height:i}}S.DefaultMagicKeysAliasMap=et,S.StorageSerializers=ke,S.TransitionPresets=fl,S.asyncComputed=Y,S.breakpointsAntDesign=Zt,S.breakpointsBootstrapV5=Kt,S.breakpointsElement=on,S.breakpointsMasterCss=tn,S.breakpointsPrimeFlex=nn,S.breakpointsQuasar=Dt,S.breakpointsSematic=en,S.breakpointsTailwind=Xt,S.breakpointsVuetify=Qt,S.breakpointsVuetifyV2=xe,S.breakpointsVuetifyV3=Jt,S.cloneFnJSON=ae,S.computedAsync=Y,S.computedInject=Ae,S.createFetch=Wn,S.createReusableTemplate=z,S.createTemplatePromise=J,S.createUnrefFn=K,S.customStorageEventName=Oe,S.defaultDocument=B,S.defaultLocation=Q,S.defaultNavigator=H,S.defaultWindow=I,S.executeTransition=yt,S.formatTimeAgo=vt,S.getSSRHandler=ye,S.mapGamepadToXbox360Controller=Jn,S.onClickOutside=Ft,S.onKeyDown=Ct,S.onKeyPressed=Vt,S.onKeyStroke=fe,S.onKeyUp=At,S.onLongPress=Lt,S.onStartTyping=$t,S.setSSRHandler=vn,S.templateRef=Wt,S.unrefElement=N,S.useActiveElement=Le,S.useAnimate=Ut,S.useAsyncQueue=Ht,S.useAsyncState=Ne,S.useBase64=zt,S.useBattery=Gt,S.useBluetooth=Yt,S.useBreakpoints=ln,S.useBroadcastChannel=rn,S.useBrowserLocation=an,S.useCached=un,S.useClipboard=sn,S.useClipboardItems=cn,S.useCloned=fn,S.useColorMode=He,S.useConfirmDialog=yn,S.useCssVar=ue,S.useCurrentElement=Be,S.useCycleList=mn,S.useDark=gn,S.useDebouncedRefHistory=Sn,S.useDeviceMotion=En,S.useDeviceOrientation=qe,S.useDevicePixelRatio=Tn,S.useDevicesList=kn,S.useDisplayMedia=On,S.useDocumentVisibility=Ge,S.useDraggable=_n,S.useDropZone=Rn,S.useElementBounding=Fn,S.useElementByPoint=Pn,S.useElementHover=Cn,S.useElementSize=Ye,S.useElementVisibility=Ke,S.useEventBus=Vn,S.useEventListener=O,S.useEventSource=In,S.useEyeDropper=Mn,S.useFavicon=Ln,S.useFetch=Je,S.useFileDialog=Bn,S.useFileSystemAccess=jn,S.useFocus=zn,S.useFocusWithin=Yn,S.useFps=Xn,S.useFullscreen=Kn,S.useGamepad=Qn,S.useGeolocation=Zn,S.useIdle=to,S.useImage=oo,S.useInfiniteScroll=lo,S.useIntersectionObserver=Xe,S.useKeyModifier=ao,S.useLocalStorage=uo,S.useMagicKeys=so,S.useManualRefHistory=ze,S.useMediaControls=fo,S.useMediaQuery=q,S.useMemoize=po,S.useMemory=yo,S.useMounted=Me,S.useMouse=tt,S.useMouseInElement=nt,S.useMousePressed=go,S.useMutationObserver=oe,S.useNavigatorLanguage=ho,S.useNetwork=ot,S.useNow=lt,S.useObjectUrl=wo,S.useOffsetPagination=bo,S.useOnline=So,S.usePageLeave=Eo,S.useParallax=To,S.useParentElement=ko,S.usePerformanceObserver=Oo,S.usePermission=de,S.usePointer=Ro,S.usePointerLock=Fo,S.usePointerSwipe=Po,S.usePreferredColorScheme=Co,S.usePreferredContrast=Vo,S.usePreferredDark=Te,S.usePreferredLanguages=Ao,S.usePreferredReducedMotion=Io,S.usePrevious=Mo,S.useRafFn=ee,S.useRefHistory=_e,S.useResizeObserver=ge,S.useScreenOrientation=at,S.useScreenSafeArea=Lo,S.useScriptTag=No,S.useScroll=De,S.useScrollLock=$o,S.useSessionStorage=Wo,S.useShare=Uo,S.useSorted=Bo,S.useSpeechRecognition=jo,S.useSpeechSynthesis=zo,S.useStepper=qo,S.useStorage=me,S.useStorageAsync=Go,S.useStyleTag=Xo,S.useSupported=x,S.useSwipe=Ko,S.useTemplateRefsList=Qo,S.useTextDirection=Zo,S.useTextSelection=el,S.useTextareaAutosize=tl,S.useThrottledRefHistory=nl,S.useTimeAgo=al,S.useTimeoutPoll=ul,S.useTimestamp=sl,S.useTitle=il,S.useTransition=vl,S.useUrlSearchParams=pl,S.useUserMedia=yl,S.useVModel=mt,S.useVModels=ml,S.useVibrate=gl,S.useVirtualList=hl,S.useWakeLock=El,S.useWebNotification=Tl,S.useWebSocket=kl,S.useWebWorker=Ol,S.useWebWorkerFn=Pl,S.useWindowFocus=Cl,S.useWindowScroll=Vl,S.useWindowSize=Al,Object.keys(d).forEach(function(e){e!=="default"&&!Object.prototype.hasOwnProperty.call(S,e)&&Object.defineProperty(S,e,{enumerable:!0,get:function(){return d[e]}})})})(this.VueUse=this.VueUse||{},VueUse,VueDemi);
